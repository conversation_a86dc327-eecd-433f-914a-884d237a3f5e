<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改测试页面 - AI调研助手</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <!-- 侧边栏内容将通过JavaScript动态生成 -->
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="navbar" id="navbar">
                <!-- 导航栏内容将通过JavaScript动态生成 -->
            </header>

            <!-- 页面内容 -->
            <div class="content">
                <div class="container">
                    <h1 style="font-size: 1.875rem; font-weight: 700; margin-bottom: 2rem;">系统修改测试</h1>

                    <!-- 测试结果展示 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3>修改完成情况</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>✅ 已完成的修改</h5>
                                    <ul style="list-style-type: none; padding-left: 0;">
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            创建了通用的下拉搜索单选框组件
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            修改了搜索页面的AI模型和搜索引擎选择界面
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            将搜索引擎选择移到了关键词卡片中
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            修改了批量任务页面的选择界面
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            合并了结果管理和任务历史功能
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            扩展了Mock API系统
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            更新了导航菜单结构
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            Prompt模板库改为模态框形式
                                        </li>
                                        <li style="padding: 0.5rem 0;">
                                            <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                                            为批量任务添加了Prompt模板库选项
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h5>🔧 技术改进</h5>
                                    <ul style="list-style-type: none; padding-left: 0;">
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-cog" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                                            移除了配置管理选项，确保用户只能从预配置选项中选择
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-shield-alt" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                                            保持了双角色权限控制（管理员/普通用户）
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-search" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                                            下拉搜索组件支持搜索过滤功能
                                        </li>
                                        <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                            <i class="fas fa-database" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                                            Mock API支持AI模型和搜索引擎的CRUD操作
                                        </li>
                                        <li style="padding: 0.5rem 0;">
                                            <i class="fas fa-layer-group" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                                            统一了任务与结果管理界面
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 功能测试 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3>功能测试</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>下拉搜索组件测试</h6>
                                    <div id="testSelectContainer" style="margin-bottom: 1rem;">
                                        <!-- 测试组件将在这里生成 -->
                                    </div>
                                    <button class="btn btn-primary btn-sm" onclick="testSearchableSelect()">测试组件</button>
                                </div>
                                <div class="col-md-4">
                                    <h6>权限控制测试</h6>
                                    <div style="margin-bottom: 1rem;">
                                        <p>当前用户角色: <span id="currentRole" class="badge badge-primary"></span></p>
                                        <p>权限状态: <span id="permissionStatus"></span></p>
                                    </div>
                                    <button class="btn btn-secondary btn-sm" onclick="testPermissions()">测试权限</button>
                                </div>
                                <div class="col-md-4">
                                    <h6>Mock API测试</h6>
                                    <div style="margin-bottom: 1rem;">
                                        <p>API状态: <span id="apiStatus" class="badge badge-secondary">未测试</span></p>
                                        <p>响应时间: <span id="responseTime">-</span></p>
                                    </div>
                                    <button class="btn btn-success btn-sm" onclick="testMockAPI()">测试API</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速导航 -->
                    <div class="card">
                        <div class="card-header">
                            <h3>快速导航到修改的页面</h3>
                        </div>
                        <div class="card-body">
                            <div class="d-flex gap-2 flex-wrap">
                                <a href="search.html" class="btn btn-outline">
                                    <i class="fas fa-search"></i> 单次搜索（已修改）
                                </a>
                                <a href="batch.html" class="btn btn-outline">
                                    <i class="fas fa-tasks"></i> 批量任务（已修改）
                                </a>
                                <a href="task-results.html" class="btn btn-outline">
                                    <i class="fas fa-chart-line"></i> 任务与结果（新页面）
                                </a>
                                <a href="settings.html" class="btn btn-outline">
                                    <i class="fas fa-cog"></i> 系统设置
                                </a>
                                <a href="dashboard.html" class="btn btn-outline">
                                    <i class="fas fa-chart-pie"></i> 返回仪表板
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/mock-api.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            if (!window.AuthManager.isLoggedIn()) {
                window.location.href = 'login.html';
                return;
            }

            // 初始化导航
            window.NavigationManager.init();
            
            // 显示当前用户信息
            updateUserInfo();
        });

        // 更新用户信息
        function updateUserInfo() {
            const currentUser = window.AuthManager.currentUser;
            if (currentUser) {
                document.getElementById('currentRole').textContent = currentUser.role === 'admin' ? '管理员' : '普通用户';
                document.getElementById('currentRole').className = `badge badge-${currentUser.role === 'admin' ? 'danger' : 'primary'}`;
            }
        }

        // 测试下拉搜索组件
        function testSearchableSelect() {
            const testData = [
                { id: 'gpt4', name: 'GPT-4', provider: 'OpenAI', description: '最新的GPT-4模型' },
                { id: 'claude3', name: 'Claude-3', provider: 'Anthropic', description: 'Claude-3 Sonnet模型' },
                { id: 'gemini', name: 'Gemini Pro', provider: 'Google', description: 'Google的Gemini Pro模型' }
            ];

            window.testSelect = window.AIResearchApp.createSearchableSelect({
                containerId: 'testSelectContainer',
                items: testData,
                placeholder: '选择AI模型...',
                searchPlaceholder: '搜索模型...',
                displayField: 'name',
                valueField: 'id',
                searchFields: ['name', 'provider', 'description'],
                onSelect: function(selectedItem) {
                    window.AIResearchApp.showNotification(`选择了: ${selectedItem.name}`, 'success');
                }
            });
        }

        // 测试权限控制
        function testPermissions() {
            const permissions = ['api_config', 'search_engine_config', 'basic_search', 'batch_tasks'];
            let statusHtml = '';
            
            permissions.forEach(permission => {
                const hasPermission = window.AuthManager.hasPermission(permission);
                statusHtml += `<div style="margin: 0.25rem 0;">
                    <span class="badge badge-${hasPermission ? 'success' : 'danger'}">${permission}</span>
                    ${hasPermission ? '✅' : '❌'}
                </div>`;
            });
            
            document.getElementById('permissionStatus').innerHTML = statusHtml;
        }

        // 测试Mock API
        async function testMockAPI() {
            const startTime = Date.now();
            document.getElementById('apiStatus').textContent = '测试中...';
            document.getElementById('apiStatus').className = 'badge badge-warning';
            
            try {
                const response = await window.MockAPI.call('GET', '/models');
                const endTime = Date.now();
                
                if (response.success) {
                    document.getElementById('apiStatus').textContent = '正常';
                    document.getElementById('apiStatus').className = 'badge badge-success';
                    document.getElementById('responseTime').textContent = `${endTime - startTime}ms`;
                    window.AIResearchApp.showNotification(`API测试成功，获取到${response.data.length}个模型`, 'success');
                } else {
                    throw new Error(response.error);
                }
            } catch (error) {
                document.getElementById('apiStatus').textContent = '错误';
                document.getElementById('apiStatus').className = 'badge badge-danger';
                document.getElementById('responseTime').textContent = '超时';
                window.AIResearchApp.showNotification(`API测试失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
