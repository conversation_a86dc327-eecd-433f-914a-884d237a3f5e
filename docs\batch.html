<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量任务 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <!-- 批量上传区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                    <i class="fas fa-upload" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    批量数据导入
                </h3>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <!-- 文件上传区域 -->
                    <div>
                        <div class="form-group">
                            <label class="form-label">Excel文件上传</label>
                            <div id="dropZone" style="border: 2px dashed var(--border-color); border-radius: 8px; padding: 2rem; text-align: center; cursor: pointer; transition: all 0.2s;">
                                <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; color: var(--text-muted); margin-bottom: 1rem;"></i>
                                <p style="margin: 0; color: var(--text-secondary);">拖拽Excel文件到此处，或点击选择文件</p>
                                <p style="font-size: 0.75rem; color: var(--text-muted); margin: 0.5rem 0 0 0;">支持 .xlsx, .xls 格式，最大50MB</p>
                                <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                            </div>
                            <div id="filePreview" class="mt-3" style="display: none;">
                                <div class="d-flex items-center gap-3 p-3 border rounded">
                                    <i class="fas fa-file-excel" style="color: #10b981; font-size: 1.5rem;"></i>
                                    <div style="flex: 1;">
                                        <p id="fileName" style="margin: 0; font-weight: 500;"></p>
                                        <p id="fileSize" style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);"></p>
                                    </div>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeFile()">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="columnMappingSection" style="display: none;">
                            <label class="form-label">数据列映射配置</label>
                            <div style="background: var(--bg-secondary); padding: 1.5rem; border-radius: 8px; border: 1px solid var(--border-color);">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 1.5rem;">
                                    <div class="form-group">
                                        <label class="form-label">关键词列 *</label>
                                        <select class="form-control form-select" id="keywordColumn">
                                            <option value="">请选择关键词列</option>
                                            <option value="A" selected>A列</option>
                                            <option value="B">B列</option>
                                            <option value="C">C列</option>
                                            <option value="D">D列</option>
                                            <option value="E">E列</option>
                                        </select>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.25rem;">
                                            指定包含搜索关键词的列
                                        </p>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">结果输出列</label>
                                        <select class="form-control form-select" id="outputColumn">
                                            <option value="">请选择输出列</option>
                                            <option value="A">A列</option>
                                            <option value="B" selected>B列</option>
                                            <option value="C">C列</option>
                                            <option value="D">D列</option>
                                            <option value="E">E列</option>
                                        </select>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.25rem;">
                                            指定AI分析结果的输出列
                                        </p>
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                                    <div class="form-group">
                                        <label class="form-label">分类列 (可选)</label>
                                        <select class="form-control form-select" id="categoryColumn">
                                            <option value="">不使用分类</option>
                                            <option value="A">A列</option>
                                            <option value="B">B列</option>
                                            <option value="C" selected>C列</option>
                                            <option value="D">D列</option>
                                            <option value="E">E列</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">备注列 (可选)</label>
                                        <select class="form-control form-select" id="notesColumn">
                                            <option value="">不使用备注</option>
                                            <option value="A">A列</option>
                                            <option value="B">B列</option>
                                            <option value="C">C列</option>
                                            <option value="D" selected>D列</option>
                                            <option value="E">E列</option>
                                        </select>
                                    </div>
                                </div>

                                <div style="margin-top: 1.5rem; padding: 1rem; background: var(--bg-primary); border-radius: 6px; border: 1px solid var(--border-light);">
                                    <h5 style="font-size: 0.875rem; font-weight: 600; margin-bottom: 0.75rem; color: var(--text-primary);">
                                        <i class="fas fa-info-circle" style="color: var(--info-color);"></i> 列映射说明
                                    </h5>
                                    <ul style="font-size: 0.75rem; color: var(--text-secondary); margin: 0; padding-left: 1.25rem;">
                                        <li>关键词列：必须包含要搜索的关键词，不能为空</li>
                                        <li>结果输出列：AI分析结果将写入此列，建议选择空列</li>
                                        <li>分类列：可用于对关键词进行分类管理</li>
                                        <li>备注列：可包含额外的说明信息</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="dataPreviewSection" style="display: none;">
                            <label class="form-label">数据预览</label>
                            <div style="background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden;">
                                <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); background: var(--bg-secondary);">
                                    <div class="d-flex justify-between items-center">
                                        <h5 style="font-size: 0.875rem; font-weight: 600; margin: 0;">Excel数据预览 (前5行)</h5>
                                        <div class="d-flex items-center gap-2">
                                            <span style="font-size: 0.75rem; color: var(--text-secondary);">数据验证:</span>
                                            <span class="badge badge-success" id="validationStatus">通过</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="overflow-x: auto;">
                                    <table class="table" style="margin: 0; font-size: 0.875rem;">
                                        <thead style="background: var(--bg-secondary);">
                                            <tr>
                                                <th style="padding: 0.75rem;">行号</th>
                                                <th style="padding: 0.75rem;">A列</th>
                                                <th style="padding: 0.75rem;">B列</th>
                                                <th style="padding: 0.75rem;">C列</th>
                                                <th style="padding: 0.75rem;">D列</th>
                                                <th style="padding: 0.75rem;">E列</th>
                                            </tr>
                                        </thead>
                                        <tbody id="previewTableBody">
                                            <tr>
                                                <td style="padding: 0.75rem; color: var(--text-secondary);">1</td>
                                                <td style="padding: 0.75rem;">人工智能芯片</td>
                                                <td style="padding: 0.75rem;"></td>
                                                <td style="padding: 0.75rem;">科技</td>
                                                <td style="padding: 0.75rem;">重点关注</td>
                                                <td style="padding: 0.75rem;"></td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 0.75rem; color: var(--text-secondary);">2</td>
                                                <td style="padding: 0.75rem;">新能源汽车</td>
                                                <td style="padding: 0.75rem;"></td>
                                                <td style="padding: 0.75rem;">汽车</td>
                                                <td style="padding: 0.75rem;">市场热点</td>
                                                <td style="padding: 0.75rem;"></td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 0.75rem; color: var(--text-secondary);">3</td>
                                                <td style="padding: 0.75rem;">区块链技术</td>
                                                <td style="padding: 0.75rem;"></td>
                                                <td style="padding: 0.75rem;">金融科技</td>
                                                <td style="padding: 0.75rem;">长期跟踪</td>
                                                <td style="padding: 0.75rem;"></td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 0.75rem; color: var(--text-secondary);">4</td>
                                                <td style="padding: 0.75rem;">元宇宙平台</td>
                                                <td style="padding: 0.75rem;"></td>
                                                <td style="padding: 0.75rem;">虚拟现实</td>
                                                <td style="padding: 0.75rem;">新兴领域</td>
                                                <td style="padding: 0.75rem;"></td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 0.75rem; color: var(--text-secondary);">5</td>
                                                <td style="padding: 0.75rem;">5G通信技术</td>
                                                <td style="padding: 0.75rem;"></td>
                                                <td style="padding: 0.75rem;">通信</td>
                                                <td style="padding: 0.75rem;">基础设施</td>
                                                <td style="padding: 0.75rem;"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div style="padding: 1rem; background: var(--bg-secondary); border-top: 1px solid var(--border-light);">
                                    <div class="d-flex justify-between items-center">
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);">
                                            <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                                            检测到 <strong>5</strong> 行有效数据，关键词列数据完整
                                        </span>
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="refreshPreview()">
                                            <i class="fas fa-sync-alt"></i> 刷新预览
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批量配置 -->
                    <div>
                        <div class="form-group">
                            <label class="form-label">批量处理配置</label>
                            <div style="background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: 8px; padding: 1rem;">
                                <div class="form-group mb-3">
                                    <label style="font-size: 0.875rem; font-weight: 500;">AI模型</label>
                                    <select class="form-control form-select" id="batchAiModel">
                                        <!-- AI模型选项将通过JavaScript动态生成 -->
                                    </select>
                                </div>

                                <div class="form-group mb-3">
                                    <label style="font-size: 0.875rem; font-weight: 500;">搜索引擎</label>
                                    <div id="batchEngineSelection" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 0.5rem;">
                                        <!-- 搜索引擎选项将通过JavaScript动态生成 -->
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label style="font-size: 0.875rem; font-weight: 500;">搜索深度</label>
                                    <select class="form-control form-select">
                                        <option value="basic">基础搜索</option>
                                        <option value="standard" selected>标准搜索</option>
                                        <option value="deep">深度搜索</option>
                                    </select>
                                </div>

                                <div class="form-group mb-3">
                                    <label style="font-size: 0.875rem; font-weight: 500;">并发数量</label>
                                    <select class="form-control form-select">
                                        <option value="1">1个任务 (稳定)</option>
                                        <option value="3" selected>3个任务 (推荐)</option>
                                        <option value="5">5个任务 (快速)</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label style="font-size: 0.875rem; font-weight: 500;">失败重试</label>
                                    <div class="d-flex items-center gap-2">
                                        <input type="checkbox" id="autoRetry" checked>
                                        <label for="autoRetry" style="font-size: 0.875rem;">自动重试失败任务</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">通用Prompt模板</label>
                            <textarea class="form-control" rows="4" placeholder="输入适用于所有关键词的分析要求...">请对{keyword}进行全面的市场调研分析，包括：
1. 市场规模和增长趋势
2. 主要竞争对手分析
3. 技术发展现状
4. 未来发展机遇与挑战</textarea>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-between items-center mt-4">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-secondary" onclick="downloadTemplate()">
                            <i class="fas fa-download"></i> 下载Excel模板
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="previewData()">
                            <i class="fas fa-eye"></i> 预览数据
                        </button>
                    </div>
                    <button type="button" class="btn btn-primary btn-lg" onclick="startBatchProcess()" disabled id="startBtn">
                        <i class="fas fa-play"></i> 开始批量处理
                    </button>
                </div>
            </div>
        </div>

        <!-- 任务队列管理 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-between items-center">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                        <i class="fas fa-list" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                        任务队列
                    </h3>
                    <div class="d-flex items-center gap-3">
                        <div class="d-flex items-center gap-2">
                            <span style="font-size: 0.875rem; color: var(--text-secondary);">总进度:</span>
                            <div class="progress" style="width: 120px;">
                                <div class="progress-bar" style="width: 35%;" id="overallProgress"></div>
                            </div>
                            <span style="font-size: 0.875rem; font-weight: 500;" id="progressText">35%</span>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-secondary btn-sm" onclick="pauseAll()">
                                <i class="fas fa-pause"></i> 暂停
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="cancelAll()">
                                <i class="fas fa-stop"></i> 停止
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table data-table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th data-sort="keyword">关键词</th>
                                <th data-sort="category">分类</th>
                                <th data-sort="priority">优先级</th>
                                <th data-sort="status">状态</th>
                                <th data-sort="progress">进度</th>
                                <th data-sort="time">预计时间</th>
                                <th style="width: 120px;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="taskTableBody">
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="1"></td>
                                <td>人工智能芯片</td>
                                <td>科技</td>
                                <td><span class="badge badge-danger">高</span></td>
                                <td><span class="badge badge-success">已完成</span></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 100%;"></div>
                                    </div>
                                </td>
                                <td>-</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-secondary btn-sm" data-tooltip="查看结果">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" data-tooltip="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="2"></td>
                                <td>新能源汽车</td>
                                <td>汽车</td>
                                <td><span class="badge badge-warning">中</span></td>
                                <td><span class="badge badge-warning">处理中</span></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 65%;"></div>
                                    </div>
                                </td>
                                <td>2分钟</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-warning btn-sm" data-tooltip="暂停">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" data-tooltip="取消">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="3"></td>
                                <td>区块链技术</td>
                                <td>金融科技</td>
                                <td><span class="badge badge-primary">中</span></td>
                                <td><span class="badge badge-primary">排队中</span></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 0%;"></div>
                                    </div>
                                </td>
                                <td>5分钟</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-primary btn-sm" data-tooltip="优先处理">
                                            <i class="fas fa-arrow-up"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" data-tooltip="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="4"></td>
                                <td>元宇宙平台</td>
                                <td>虚拟现实</td>
                                <td><span class="badge badge-primary">低</span></td>
                                <td><span class="badge badge-danger">失败</span></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 0%; background-color: var(--danger-color);"></div>
                                    </div>
                                </td>
                                <td>-</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-success btn-sm" data-tooltip="重试">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" data-tooltip="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 记录访问活动
            window.AuthManager.logActivity('访问批量任务页面');

            // 初始化AI模型和搜索引擎选项
            initializeBatchAIModels();
            initializeBatchSearchEngines();
        });

        // 初始化批量任务AI模型选项
        function initializeBatchAIModels() {
            const select = document.getElementById('batchAiModel');
            const models = window.AuthManager.getAvailableAIModels();

            select.innerHTML = '<option value="">请选择AI模型</option>';

            models.forEach((model, index) => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = `${model.name} (${model.provider})`;
                option.title = model.description || '';
                if (index === 0) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }

        // 初始化批量任务搜索引擎选项
        function initializeBatchSearchEngines() {
            const container = document.getElementById('batchEngineSelection');
            const engines = window.AuthManager.getAvailableSearchEngines();

            container.innerHTML = '';

            engines.forEach((engine, index) => {
                const label = document.createElement('label');
                label.className = 'd-flex items-center gap-1 p-1 border rounded cursor-pointer';
                label.style.fontSize = '0.75rem';

                const iconClass = getBatchEngineIcon(engine.id);
                const iconColor = getBatchEngineColor(engine.id);

                label.innerHTML = `
                    <input type="checkbox" name="batchEngines" value="${engine.id}" ${index < 2 ? 'checked' : ''}>
                    <i class="${iconClass}" style="color: ${iconColor}; font-size: 0.875rem;"></i>
                    <span>${engine.name}</span>
                `;

                container.appendChild(label);
            });
        }

        // 获取批量任务搜索引擎图标
        function getBatchEngineIcon(engineId) {
            const icons = {
                'google': 'fab fa-google',
                'bing': 'fab fa-microsoft',
                'baidu': 'fas fa-search',
                'duckduckgo': 'fas fa-search',
                'yandex': 'fas fa-search',
                'scholar': 'fas fa-graduation-cap'
            };
            return icons[engineId] || 'fas fa-search';
        }

        // 获取批量任务搜索引擎颜色
        function getBatchEngineColor(engineId) {
            const colors = {
                'google': '#4285f4',
                'bing': '#00a1f1',
                'baidu': '#2932e1',
                'duckduckgo': '#de5833',
                'yandex': '#fc3f1d',
                'scholar': '#4285f4'
            };
            return colors[engineId] || '#666';
        }

        // 文件上传处理
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const filePreview = document.getElementById('filePreview');
        const startBtn = document.getElementById('startBtn');

        dropZone.addEventListener('click', () => fileInput.click());
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = 'var(--primary-color)';
            dropZone.style.backgroundColor = 'rgb(59 130 246 / 0.05)';
        });
        dropZone.addEventListener('dragleave', () => {
            dropZone.style.borderColor = 'var(--border-color)';
            dropZone.style.backgroundColor = 'transparent';
        });
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = 'var(--border-color)';
            dropZone.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file.name.match(/\.(xlsx|xls)$/)) {
                AIResearchApp.showNotification('请选择Excel文件', 'warning');
                return;
            }

            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = AIResearchApp.utils.formatFileSize ? AIResearchApp.utils.formatFileSize(file.size) : `${(file.size / 1024 / 1024).toFixed(2)} MB`;
            filePreview.style.display = 'block';

            // 显示列映射配置
            document.getElementById('columnMappingSection').style.display = 'block';
            document.getElementById('dataPreviewSection').style.display = 'block';

            // 模拟Excel数据解析
            parseExcelFile(file);

            startBtn.disabled = false;
        }

        function removeFile() {
            filePreview.style.display = 'none';
            fileInput.value = '';

            // 隐藏列映射和预览区域
            document.getElementById('columnMappingSection').style.display = 'none';
            document.getElementById('dataPreviewSection').style.display = 'none';

            startBtn.disabled = true;
        }

        // 解析Excel文件 (模拟)
        function parseExcelFile(file) {
            // 这里应该使用实际的Excel解析库，如SheetJS
            // 现在使用模拟数据
            const mockData = [
                ['人工智能芯片', '', '科技', '重点关注', ''],
                ['新能源汽车', '', '汽车', '市场热点', ''],
                ['区块链技术', '', '金融科技', '长期跟踪', ''],
                ['元宇宙平台', '', '虚拟现实', '新兴领域', ''],
                ['5G通信技术', '', '通信', '基础设施', '']
            ];

            updatePreviewTable(mockData);
            validateData(mockData);
        }

        // 更新预览表格
        function updatePreviewTable(data) {
            const tbody = document.getElementById('previewTableBody');
            tbody.innerHTML = '';

            data.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td style="padding: 0.75rem; color: var(--text-secondary);">${index + 1}</td>
                    <td style="padding: 0.75rem;">${row[0] || ''}</td>
                    <td style="padding: 0.75rem;">${row[1] || ''}</td>
                    <td style="padding: 0.75rem;">${row[2] || ''}</td>
                    <td style="padding: 0.75rem;">${row[3] || ''}</td>
                    <td style="padding: 0.75rem;">${row[4] || ''}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        // 验证数据
        function validateData(data) {
            const keywordColumn = document.getElementById('keywordColumn').value;
            const validationStatus = document.getElementById('validationStatus');

            if (!keywordColumn) {
                validationStatus.textContent = '请选择关键词列';
                validationStatus.className = 'badge badge-warning';
                return false;
            }

            const columnIndex = keywordColumn.charCodeAt(0) - 65; // A=0, B=1, etc.
            let validRows = 0;
            let emptyRows = 0;

            data.forEach(row => {
                if (row[columnIndex] && row[columnIndex].trim()) {
                    validRows++;
                } else {
                    emptyRows++;
                }
            });

            if (emptyRows > 0) {
                validationStatus.textContent = `${emptyRows}行关键词为空`;
                validationStatus.className = 'badge badge-warning';
            } else {
                validationStatus.textContent = '验证通过';
                validationStatus.className = 'badge badge-success';
            }

            return emptyRows === 0;
        }

        // 刷新预览
        function refreshPreview() {
            if (fileInput.files.length > 0) {
                parseExcelFile(fileInput.files[0]);
                AIResearchApp.showNotification('预览已刷新', 'success');
            }
        }

        // 列映射变化监听
        document.getElementById('keywordColumn').addEventListener('change', function() {
            if (fileInput.files.length > 0) {
                parseExcelFile(fileInput.files[0]);
            }
        });

        document.getElementById('outputColumn').addEventListener('change', function() {
            // 检查输出列是否与关键词列冲突
            const keywordColumn = document.getElementById('keywordColumn').value;
            const outputColumn = this.value;

            if (keywordColumn && outputColumn && keywordColumn === outputColumn) {
                AIResearchApp.showNotification('输出列不能与关键词列相同', 'warning');
                this.value = '';
            }
        });

        function downloadTemplate() {
            // 模拟下载Excel模板
            const link = document.createElement('a');
            link.href = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,';
            link.download = 'AI调研助手_批量导入模板.xlsx';
            AIResearchApp.showNotification('模板下载已开始', 'success');
        }

        function previewData() {
            if (!fileInput.files.length) {
                AIResearchApp.showNotification('请先选择文件', 'warning');
                return;
            }
            AIResearchApp.showNotification('数据预览功能开发中', 'info');
        }

        function startBatchProcess() {
            if (!fileInput.files.length) {
                AIResearchApp.showNotification('请先上传Excel文件', 'warning');
                return;
            }

            // 验证列映射配置
            const keywordColumn = document.getElementById('keywordColumn').value;
            const outputColumn = document.getElementById('outputColumn').value;

            if (!keywordColumn) {
                AIResearchApp.showNotification('请选择关键词列', 'warning');
                return;
            }

            if (!outputColumn) {
                AIResearchApp.showNotification('请选择结果输出列', 'warning');
                return;
            }

            if (keywordColumn === outputColumn) {
                AIResearchApp.showNotification('关键词列和输出列不能相同', 'warning');
                return;
            }

            // 验证数据
            const mockData = [
                ['人工智能芯片', '', '科技', '重点关注', ''],
                ['新能源汽车', '', '汽车', '市场热点', ''],
                ['区块链技术', '', '金融科技', '长期跟踪', ''],
                ['元宇宙平台', '', '虚拟现实', '新兴领域', ''],
                ['5G通信技术', '', '通信', '基础设施', '']
            ];

            if (!validateData(mockData)) {
                AIResearchApp.showNotification('数据验证失败，请检查关键词列是否包含有效数据', 'error');
                return;
            }

            if (confirm('确定开始批量处理吗？这可能需要较长时间。')) {
                startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                startBtn.disabled = true;

                // 记录批量任务开始
                if (window.AuthManager.isLoggedIn()) {
                    window.AuthManager.logActivity('开始批量任务', {
                        fileName: fileInput.files[0].name,
                        keywordColumn: keywordColumn,
                        outputColumn: outputColumn,
                        rowCount: mockData.length
                    });
                }

                // 模拟批量处理
                setTimeout(() => {
                    AIResearchApp.showNotification('批量任务已添加到队列', 'success');
                    startBtn.innerHTML = '<i class="fas fa-play"></i> 开始批量处理';
                    startBtn.disabled = false;

                    // 可选：跳转到任务历史页面
                    if (confirm('任务已提交，是否查看任务进度？')) {
                        window.location.href = 'task-history.html';
                    }
                }, 2000);
            }
        }

        function pauseAll() {
            AIResearchApp.showNotification('所有任务已暂停', 'info');
        }

        function cancelAll() {
            if (confirm('确定要停止所有任务吗？')) {
                AIResearchApp.showNotification('所有任务已停止', 'warning');
            }
        }

        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="taskSelect"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });

        // 模拟进度更新
        setInterval(() => {
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const currentWidth = parseInt(bar.style.width);
                if (currentWidth > 0 && currentWidth < 100) {
                    bar.style.width = Math.min(currentWidth + Math.random() * 5, 100) + '%';
                }
            });
        }, 2000);
    </script>
</body>
</html>
