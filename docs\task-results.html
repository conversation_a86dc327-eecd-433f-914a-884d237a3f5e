<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务与结果管理 - AI调研助手</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <!-- 侧边栏内容将通过JavaScript动态生成 -->
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="navbar" id="navbar">
                <!-- 导航栏内容将通过JavaScript动态生成 -->
            </header>

            <!-- 页面内容 -->
            <div class="content">
                <div class="container">
                    <!-- 页面标题和操作 -->
                    <div class="d-flex justify-between items-center mb-4">
                        <div>
                            <h1 style="font-size: 1.875rem; font-weight: 700; margin: 0;">任务与结果管理</h1>
                            <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0;">管理您的搜索任务和分析结果</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-secondary" onclick="exportSelected()">
                                <i class="fas fa-download"></i> 导出选中
                            </button>
                            <button class="btn btn-danger" onclick="deleteSelected()">
                                <i class="fas fa-trash"></i> 删除选中
                            </button>
                        </div>
                    </div>

                    <!-- 标签页导航 -->
                    <div class="card">
                        <div class="card-header" style="padding: 0; border-bottom: 1px solid var(--border-color);">
                            <div class="tab-nav" style="display: flex; border-bottom: none;">
                                <button class="tab-btn active" onclick="switchTab('tasks')" id="tasksTab">
                                    <i class="fas fa-tasks"></i> 任务历史
                                </button>
                                <button class="tab-btn" onclick="switchTab('results')" id="resultsTab">
                                    <i class="fas fa-table"></i> 分析结果
                                </button>
                            </div>
                        </div>

                        <!-- 筛选和搜索 -->
                        <div class="card-body" style="border-bottom: 1px solid var(--border-color);">
                            <div class="d-flex gap-3 flex-wrap">
                                <div class="form-group" style="margin: 0; min-width: 200px;">
                                    <input type="text" class="form-control" placeholder="搜索关键词..." id="searchInput">
                                </div>
                                <div class="form-group" style="margin: 0; min-width: 120px;">
                                    <select class="form-control form-select" id="statusFilter">
                                        <option value="">所有状态</option>
                                        <option value="completed">已完成</option>
                                        <option value="running">运行中</option>
                                        <option value="failed">失败</option>
                                        <option value="pending">等待中</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin: 0; min-width: 120px;">
                                    <select class="form-control form-select" id="typeFilter">
                                        <option value="">所有类型</option>
                                        <option value="single">单次搜索</option>
                                        <option value="batch">批量任务</option>
                                        <option value="scheduled">定时任务</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin: 0; min-width: 120px;">
                                    <select class="form-control form-select" id="timeFilter">
                                        <option value="">所有时间</option>
                                        <option value="today">今天</option>
                                        <option value="week">本周</option>
                                        <option value="month">本月</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" onclick="applyFilters()">
                                    <i class="fas fa-filter"></i> 筛选
                                </button>
                                <button class="btn btn-secondary" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                            </div>
                        </div>

                        <!-- 任务历史标签页内容 -->
                        <div class="tab-content" id="tasksContent">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th style="width: 40px;">
                                                <input type="checkbox" id="selectAllTasks">
                                            </th>
                                            <th>任务名称</th>
                                            <th>类型</th>
                                            <th>状态</th>
                                            <th>进度</th>
                                            <th>创建时间</th>
                                            <th>完成时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tasksTableBody">
                                        <!-- 任务数据将通过JavaScript动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 分析结果标签页内容 -->
                        <div class="tab-content" id="resultsContent" style="display: none;">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th style="width: 40px;">
                                                <input type="checkbox" id="selectAllResults">
                                            </th>
                                            <th>关键词</th>
                                            <th>AI模型</th>
                                            <th>搜索引擎</th>
                                            <th>质量评分</th>
                                            <th>创建时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultsTableBody">
                                        <!-- 结果数据将通过JavaScript动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div class="card-footer">
                            <div class="d-flex justify-between items-center">
                                <div style="color: var(--text-secondary); font-size: 0.875rem;">
                                    显示 <span id="currentRange">1-10</span> 条，共 <span id="totalCount">0</span> 条记录
                                </div>
                                <div class="pagination" id="pagination">
                                    <!-- 分页控件将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 任务详情模态框 -->
    <div class="modal" id="taskDetailModal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3>任务详情</h3>
                <button class="btn btn-secondary btn-sm" data-modal-close>
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="taskDetailContent">
                <!-- 任务详情内容将动态生成 -->
            </div>
        </div>
    </div>

    <!-- 结果详情模态框 -->
    <div class="modal" id="resultDetailModal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-content" style="max-width: 1000px;">
            <div class="modal-header">
                <h3>分析结果详情</h3>
                <button class="btn btn-secondary btn-sm" data-modal-close>
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="resultDetailContent">
                <!-- 结果详情内容将动态生成 -->
            </div>
        </div>
    </div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/mock-api.js"></script>
    <script>
        let currentTab = 'tasks';
        let currentPage = 1;
        const pageSize = 10;
        let allTasks = [];
        let allResults = [];
        let filteredData = [];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            if (!window.AuthManager.isLoggedIn()) {
                window.location.href = 'login.html';
                return;
            }

            // 初始化导航
            window.NavigationManager.init();
            
            // 加载数据
            loadTasksData();
            loadResultsData();
            
            // 渲染当前标签页
            renderCurrentTab();
            
            // 绑定事件
            bindEvents();
        });

        // 标签页切换
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tab + 'Tab').classList.add('active');
            
            // 显示/隐藏内容
            document.getElementById('tasksContent').style.display = tab === 'tasks' ? 'block' : 'none';
            document.getElementById('resultsContent').style.display = tab === 'results' ? 'block' : 'none';
            
            // 重置页码
            currentPage = 1;
            
            // 渲染数据
            renderCurrentTab();
        }

        // 渲染当前标签页
        function renderCurrentTab() {
            if (currentTab === 'tasks') {
                renderTasks();
            } else {
                renderResults();
            }
        }

        // 加载任务数据
        async function loadTasksData() {
            try {
                const response = await window.MockAPI.call('GET', '/tasks');
                if (response.success) {
                    allTasks = response.data;
                }
            } catch (error) {
                console.error('加载任务数据失败:', error);
                allTasks = getMockTasks();
            }
        }

        // 加载结果数据
        async function loadResultsData() {
            try {
                const response = await window.MockAPI.call('GET', '/results');
                if (response.success) {
                    allResults = response.data;
                }
            } catch (error) {
                console.error('加载结果数据失败:', error);
                allResults = getMockResults();
            }
        }

        // 获取模拟任务数据
        function getMockTasks() {
            return [
                {
                    id: 'task_1',
                    name: '人工智能市场分析',
                    type: 'single',
                    status: 'completed',
                    progress: 100,
                    keyword: '人工智能',
                    createdAt: '2024-01-15T10:30:00Z',
                    completedAt: '2024-01-15T10:35:00Z',
                    model: 'GPT-4',
                    engine: 'Google'
                },
                {
                    id: 'task_2',
                    name: '批量关键词分析',
                    type: 'batch',
                    status: 'running',
                    progress: 65,
                    keyword: '多个关键词',
                    createdAt: '2024-01-15T11:00:00Z',
                    completedAt: null,
                    model: 'Claude-3',
                    engine: 'Bing'
                }
            ];
        }

        // 获取模拟结果数据
        function getMockResults() {
            return [
                {
                    id: 'result_1',
                    keyword: '人工智能',
                    model: 'GPT-4',
                    engine: 'Google',
                    quality: 95,
                    createdAt: '2024-01-15T10:35:00Z',
                    status: 'completed',
                    summary: '人工智能市场规模持续增长...'
                },
                {
                    id: 'result_2',
                    keyword: '机器学习',
                    model: 'Claude-3',
                    engine: 'Bing',
                    quality: 88,
                    createdAt: '2024-01-15T09:20:00Z',
                    status: 'completed',
                    summary: '机器学习技术在各行业应用广泛...'
                }
            ];
        }

        // 渲染任务列表
        function renderTasks() {
            const tbody = document.getElementById('tasksTableBody');
            const data = applyFilters(allTasks);
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = data.slice(startIndex, endIndex);

            tbody.innerHTML = '';

            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                            <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <div>暂无任务数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            pageData.forEach(task => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="checkbox" name="taskSelect" value="${task.id}">
                    </td>
                    <td>
                        <div style="font-weight: 500;">${task.name}</div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">${task.keyword}</div>
                    </td>
                    <td>
                        <span class="badge badge-info">${getTaskTypeName(task.type)}</span>
                    </td>
                    <td>
                        <span class="badge badge-${getStatusColor(task.status)}">${getStatusName(task.status)}</span>
                    </td>
                    <td>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar" style="width: ${task.progress}%"></div>
                        </div>
                        <div style="font-size: 0.75rem; text-align: center; margin-top: 0.25rem;">${task.progress}%</div>
                    </td>
                    <td>${formatDateTime(task.createdAt)}</td>
                    <td>${task.completedAt ? formatDateTime(task.completedAt) : '-'}</td>
                    <td>
                        <div class="d-flex gap-1">
                            <button class="btn btn-secondary btn-sm" onclick="viewTask('${task.id}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="downloadTask('${task.id}')" title="下载结果">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="retryTask('${task.id}')" title="重新执行">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteTask('${task.id}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updatePagination(data.length);
        }

        // 渲染结果列表
        function renderResults() {
            const tbody = document.getElementById('resultsTableBody');
            const data = applyFilters(allResults);
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = data.slice(startIndex, endIndex);

            tbody.innerHTML = '';

            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                            <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <div>暂无结果数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            pageData.forEach(result => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="checkbox" name="resultSelect" value="${result.id}">
                    </td>
                    <td>
                        <div style="font-weight: 500;">${result.keyword}</div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">${result.summary.substring(0, 50)}...</div>
                    </td>
                    <td>${result.model}</td>
                    <td>${result.engine}</td>
                    <td>
                        <div class="d-flex items-center gap-2">
                            <div class="progress" style="flex: 1; height: 6px;">
                                <div class="progress-bar bg-${getQualityColor(result.quality)}" style="width: ${result.quality}%"></div>
                            </div>
                            <span style="font-size: 0.75rem; font-weight: 500;">${result.quality}</span>
                        </div>
                    </td>
                    <td>${formatDateTime(result.createdAt)}</td>
                    <td>
                        <span class="badge badge-${getStatusColor(result.status)}">${getStatusName(result.status)}</span>
                    </td>
                    <td>
                        <div class="d-flex gap-1">
                            <button class="btn btn-secondary btn-sm" onclick="viewResult('${result.id}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="downloadResult('${result.id}')" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-success btn-sm" onclick="shareResult('${result.id}')" title="分享">
                                <i class="fas fa-share"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteResult('${result.id}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updatePagination(data.length);
        }

        // 应用筛选条件
        function applyFilters(data) {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const timeFilter = document.getElementById('timeFilter').value;

            return data.filter(item => {
                // 搜索关键词筛选
                if (searchTerm) {
                    const searchableText = (item.name || item.keyword || '').toLowerCase() +
                                         (item.keyword || '').toLowerCase() +
                                         (item.summary || '').toLowerCase();
                    if (!searchableText.includes(searchTerm)) {
                        return false;
                    }
                }

                // 状态筛选
                if (statusFilter && item.status !== statusFilter) {
                    return false;
                }

                // 类型筛选（仅对任务有效）
                if (typeFilter && item.type && item.type !== typeFilter) {
                    return false;
                }

                // 时间筛选
                if (timeFilter) {
                    const itemDate = new Date(item.createdAt);
                    const now = new Date();

                    switch (timeFilter) {
                        case 'today':
                            if (itemDate.toDateString() !== now.toDateString()) {
                                return false;
                            }
                            break;
                        case 'week':
                            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                            if (itemDate < weekAgo) {
                                return false;
                            }
                            break;
                        case 'month':
                            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                            if (itemDate < monthAgo) {
                                return false;
                            }
                            break;
                    }
                }

                return true;
            });
        }

        // 更新分页
        function updatePagination(totalCount) {
            const totalPages = Math.ceil(totalCount / pageSize);
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, totalCount);

            // 更新计数显示
            document.getElementById('currentRange').textContent = `${startIndex}-${endIndex}`;
            document.getElementById('totalCount').textContent = totalCount;

            // 生成分页控件
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) return;

            // 上一页
            if (currentPage > 1) {
                const prevBtn = document.createElement('button');
                prevBtn.className = 'btn btn-secondary btn-sm';
                prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
                prevBtn.onclick = () => changePage(currentPage - 1);
                pagination.appendChild(prevBtn);
            }

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-secondary'}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => changePage(i);
                pagination.appendChild(pageBtn);
            }

            // 下一页
            if (currentPage < totalPages) {
                const nextBtn = document.createElement('button');
                nextBtn.className = 'btn btn-secondary btn-sm';
                nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
                nextBtn.onclick = () => changePage(currentPage + 1);
                pagination.appendChild(nextBtn);
            }
        }

        // 切换页码
        function changePage(page) {
            currentPage = page;
            renderCurrentTab();
        }

        // 工具函数
        function getTaskTypeName(type) {
            const names = {
                'single': '单次搜索',
                'batch': '批量任务',
                'scheduled': '定时任务'
            };
            return names[type] || type;
        }

        function getStatusName(status) {
            const names = {
                'completed': '已完成',
                'running': '运行中',
                'failed': '失败',
                'pending': '等待中'
            };
            return names[status] || status;
        }

        function getStatusColor(status) {
            const colors = {
                'completed': 'success',
                'running': 'primary',
                'failed': 'danger',
                'pending': 'warning'
            };
            return colors[status] || 'secondary';
        }

        function getQualityColor(quality) {
            if (quality >= 90) return 'success';
            if (quality >= 70) return 'warning';
            return 'danger';
        }

        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 事件绑定
        function bindEvents() {
            // 全选功能
            document.getElementById('selectAllTasks').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('input[name="taskSelect"]');
                checkboxes.forEach(cb => cb.checked = this.checked);
            });

            document.getElementById('selectAllResults').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('input[name="resultSelect"]');
                checkboxes.forEach(cb => cb.checked = this.checked);
            });

            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', function() {
                currentPage = 1;
                renderCurrentTab();
            });
        }

        // 操作函数
        function applyFilters() {
            currentPage = 1;
            renderCurrentTab();
            window.AIResearchApp.showNotification('筛选已应用', 'success');
        }

        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('timeFilter').value = '';
            currentPage = 1;
            renderCurrentTab();
            window.AIResearchApp.showNotification('筛选已重置', 'info');
        }

        function exportSelected() {
            const selectedItems = getSelectedItems();
            if (selectedItems.length === 0) {
                window.AIResearchApp.showNotification('请先选择要导出的项目', 'warning');
                return;
            }
            window.AIResearchApp.showNotification(`正在导出 ${selectedItems.length} 个项目...`, 'info');
        }

        function deleteSelected() {
            const selectedItems = getSelectedItems();
            if (selectedItems.length === 0) {
                window.AIResearchApp.showNotification('请先选择要删除的项目', 'warning');
                return;
            }

            if (confirm(`确定要删除选中的 ${selectedItems.length} 个项目吗？`)) {
                window.AIResearchApp.showNotification(`已删除 ${selectedItems.length} 个项目`, 'success');
                renderCurrentTab();
            }
        }

        function getSelectedItems() {
            const checkboxName = currentTab === 'tasks' ? 'taskSelect' : 'resultSelect';
            const checkboxes = document.querySelectorAll(`input[name="${checkboxName}"]:checked`);
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 任务操作
        function viewTask(id) {
            const task = allTasks.find(t => t.id === id);
            if (task) {
                showTaskDetail(task);
            }
        }

        function downloadTask(id) {
            window.AIResearchApp.showNotification(`正在下载任务 ${id} 的结果`, 'success');
        }

        function retryTask(id) {
            if (confirm('确定要重新执行这个任务吗？')) {
                window.AIResearchApp.showNotification(`任务 ${id} 已重新启动`, 'success');
            }
        }

        function deleteTask(id) {
            if (confirm('确定要删除这个任务吗？')) {
                window.AIResearchApp.showNotification(`任务 ${id} 已删除`, 'warning');
                renderCurrentTab();
            }
        }

        // 结果操作
        function viewResult(id) {
            const result = allResults.find(r => r.id === id);
            if (result) {
                showResultDetail(result);
            }
        }

        function downloadResult(id) {
            window.AIResearchApp.showNotification(`正在下载结果 ${id}`, 'success');
        }

        function shareResult(id) {
            window.AIResearchApp.showNotification('分享链接已复制到剪贴板', 'success');
        }

        function deleteResult(id) {
            if (confirm('确定要删除这个结果吗？')) {
                window.AIResearchApp.showNotification(`结果 ${id} 已删除`, 'warning');
                renderCurrentTab();
            }
        }

        // 显示任务详情
        function showTaskDetail(task) {
            const modal = document.getElementById('taskDetailModal');
            const content = document.getElementById('taskDetailContent');

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h5>基本信息</h5>
                        <table class="table table-borderless">
                            <tr><td><strong>任务名称:</strong></td><td>${task.name}</td></tr>
                            <tr><td><strong>任务类型:</strong></td><td>${getTaskTypeName(task.type)}</td></tr>
                            <tr><td><strong>关键词:</strong></td><td>${task.keyword}</td></tr>
                            <tr><td><strong>AI模型:</strong></td><td>${task.model}</td></tr>
                            <tr><td><strong>搜索引擎:</strong></td><td>${task.engine}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>执行状态</h5>
                        <table class="table table-borderless">
                            <tr><td><strong>状态:</strong></td><td><span class="badge badge-${getStatusColor(task.status)}">${getStatusName(task.status)}</span></td></tr>
                            <tr><td><strong>进度:</strong></td><td>${task.progress}%</td></tr>
                            <tr><td><strong>创建时间:</strong></td><td>${formatDateTime(task.createdAt)}</td></tr>
                            <tr><td><strong>完成时间:</strong></td><td>${task.completedAt ? formatDateTime(task.completedAt) : '未完成'}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="mt-3">
                    <h5>执行日志</h5>
                    <div style="background: var(--bg-secondary); padding: 1rem; border-radius: 6px; font-family: monospace; font-size: 0.875rem;">
                        [${formatDateTime(task.createdAt)}] 任务创建<br>
                        [${formatDateTime(task.createdAt)}] 开始搜索数据<br>
                        [${formatDateTime(task.createdAt)}] AI分析中...<br>
                        ${task.completedAt ? `[${formatDateTime(task.completedAt)}] 任务完成` : '[进行中] 正在处理...'}
                    </div>
                </div>
            `;

            window.AIResearchApp.showModal(modal);
        }

        // 显示结果详情
        function showResultDetail(result) {
            const modal = document.getElementById('resultDetailModal');
            const content = document.getElementById('resultDetailContent');

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <h5>基本信息</h5>
                        <table class="table table-borderless">
                            <tr><td><strong>关键词:</strong></td><td>${result.keyword}</td></tr>
                            <tr><td><strong>AI模型:</strong></td><td>${result.model}</td></tr>
                            <tr><td><strong>搜索引擎:</strong></td><td>${result.engine}</td></tr>
                            <tr><td><strong>质量评分:</strong></td><td>${result.quality}/100</td></tr>
                            <tr><td><strong>创建时间:</strong></td><td>${formatDateTime(result.createdAt)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-8">
                        <h5>分析结果</h5>
                        <div style="background: var(--bg-secondary); padding: 1rem; border-radius: 6px; max-height: 400px; overflow-y: auto;">
                            <p>${result.summary}</p>
                            <p>这是一个详细的分析结果示例。在实际应用中，这里会显示AI模型生成的完整分析报告，包括市场规模、发展趋势、竞争格局、技术发展现状等多个维度的深入分析。</p>
                            <p>分析结果会根据选择的AI模型和搜索引擎的不同而有所差异，系统会自动评估结果的质量并给出相应的评分。</p>
                        </div>
                    </div>
                </div>
            `;

            window.AIResearchApp.showModal(modal);
        }
    </script>
</body>
</html>
    </script>
</body>
</html>
