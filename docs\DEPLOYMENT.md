# AI调研助手 - 部署指南

## 概述

本文档提供了AI调研助手系统的完整部署指南，包括开发环境设置、生产环境部署和配置说明。

## 🚀 快速开始

### 本地开发环境

#### 1. 环境要求
- 现代Web浏览器（Chrome 90+, Firefox 88+, Safari 14+, Edge 90+）
- 本地Web服务器（可选，用于避免CORS问题）

#### 2. 快速启动
```bash
# 克隆或下载项目
cd ai-research-assistant

# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx serve .

# 或使用PHP
php -S localhost:8000
```

#### 3. 访问系统
- 打开浏览器访问: `http://localhost:8000`
- 使用演示账户登录:
  - 管理员: `admin` / `admin`
  - 普通用户: `user` / `user`

## 📁 项目结构

```
docs/
├── index.html              # 首页
├── login.html              # 登录页面
├── dashboard.html          # 仪表板
├── search.html             # 单次搜索
├── batch.html              # 批量任务
├── task-management.html    # 任务管理 (新)
├── prompt-templates.html   # Prompt模板库 (新)
├── model-config.html       # 模型配置 (新)
├── results.html            # 结果管理
├── task-history.html       # 任务历史
├── task-detail.html        # 任务详情
├── settings.html           # 设置配置
├── admin.html              # 系统管理
├── profile.html            # 个人中心
├── intro.html              # 产品介绍
├── assets/
│   ├── css/
│   │   └── style.css       # 主样式文件
│   ├── js/
│   │   ├── auth.js         # 权限管理 (更新)
│   │   ├── navigation.js   # 导航管理 (更新)
│   │   ├── app.js          # 应用核心
│   │   └── mock-api.js     # Mock API系统 (新)
│   └── images/             # 图片资源
├── README.md               # 项目说明 (更新)
├── TESTING.md              # 测试指南 (新)
└── DEPLOYMENT.md           # 部署指南 (新)
```

## 🔧 配置说明

### Mock API配置

在 `assets/js/mock-api.js` 中可以配置：

```javascript
// API配置
const API_CONFIG = {
    baseUrl: '/api/v1',
    timeout: 2000,        // 模拟网络延迟
    mockMode: true,       // 是否启用Mock模式
    version: '1.0.0'
};
```

### 权限系统配置

在 `assets/js/auth.js` 中可以配置：

```javascript
// 添加新权限
const PERMISSIONS = {
    // 现有权限...
    NEW_PERMISSION: 'new_permission'
};

// 配置角色权限
const ROLE_PERMISSIONS = {
    [USER_ROLES.ADMIN]: [
        // 管理员权限列表
    ],
    [USER_ROLES.USER]: [
        // 普通用户权限列表
    ]
};
```

### 默认数据配置

系统启动时会自动创建默认数据，包括：
- 演示用户账户
- 默认AI模型配置
- 默认搜索引擎配置
- 示例Prompt模板

## 🌐 生产环境部署

### 1. 静态文件部署

#### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/ai-research-assistant/docs;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_types text/css application/javascript application/json;

    # 缓存静态资源
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

#### Apache配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/ai-research-assistant/docs
    
    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
    </Location>
    
    # 缓存配置
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
</VirtualHost>
```

### 2. CDN部署

#### 使用Vercel
```bash
# 安装Vercel CLI
npm i -g vercel

# 部署
cd docs
vercel --prod
```

#### 使用Netlify
```bash
# 安装Netlify CLI
npm i -g netlify-cli

# 部署
cd docs
netlify deploy --prod --dir .
```

### 3. 容器化部署

#### Dockerfile
```dockerfile
FROM nginx:alpine

# 复制静态文件
COPY docs/ /usr/share/nginx/html/

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  ai-research-assistant:
    build: .
    ports:
      - "80:80"
    restart: unless-stopped
```

## 🔒 安全配置

### 1. HTTPS配置

#### Let's Encrypt (Certbot)
```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 安全头配置

#### Nginx安全头
```nginx
# 安全头配置
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

### 3. 访问控制

#### IP白名单（如需要）
```nginx
# 限制访问IP
location / {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;
    
    try_files $uri $uri/ /index.html;
}
```

## 🔄 生产环境适配

### 1. 替换Mock API

当准备连接真实后端时：

```javascript
// 在mock-api.js中
const API_CONFIG = {
    baseUrl: 'https://your-api-server.com/api/v1',
    timeout: 10000,
    mockMode: false,  // 禁用Mock模式
    version: '1.0.0'
};
```

### 2. 环境变量配置

创建配置文件 `config.js`：

```javascript
window.APP_CONFIG = {
    API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:3000/api',
    MOCK_MODE: process.env.MOCK_MODE === 'true',
    VERSION: process.env.APP_VERSION || '1.0.0'
};
```

### 3. 数据库集成

后端API应该提供以下端点：

```
# 用户认证
POST /auth/login
POST /auth/logout
GET  /auth/profile

# 任务管理
GET    /tasks
POST   /tasks
GET    /tasks/:id
PUT    /tasks/:id
DELETE /tasks/:id

# Prompt模板
GET    /templates
POST   /templates
PUT    /templates/:id
DELETE /templates/:id

# 模型配置
GET    /models
POST   /models
PUT    /models/:id
DELETE /models/:id

# 搜索引擎配置
GET    /engines
POST   /engines
PUT    /engines/:id
DELETE /engines/:id
```

## 📊 监控和维护

### 1. 日志配置

#### Nginx访问日志
```nginx
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for"';

access_log /var/log/nginx/access.log main;
error_log /var/log/nginx/error.log;
```

### 2. 性能监控

#### 添加性能监控脚本
```javascript
// 在app.js中添加
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page load time:', perfData.loadEventEnd - perfData.fetchStart);
        }, 0);
    });
}
```

### 3. 错误监控

#### 全局错误处理
```javascript
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    // 发送错误报告到监控服务
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
    // 发送错误报告到监控服务
});
```

## 🔧 故障排除

### 常见问题

1. **页面空白**
   - 检查浏览器控制台错误
   - 确认所有资源文件路径正确
   - 检查Web服务器配置

2. **权限问题**
   - 清除浏览器localStorage
   - 检查用户角色配置
   - 验证权限检查逻辑

3. **Mock API不工作**
   - 确认mock-api.js已正确加载
   - 检查API_CONFIG.mockMode设置
   - 查看浏览器控制台错误

4. **样式问题**
   - 检查CSS文件加载
   - 验证响应式断点
   - 确认浏览器兼容性

### 调试工具

1. **浏览器开发者工具**
   - Network标签：检查资源加载
   - Console标签：查看JavaScript错误
   - Application标签：检查localStorage数据

2. **性能分析**
   - Lighthouse：页面性能评估
   - Performance标签：运行时性能分析

## 📞 技术支持

如遇到部署问题，请检查：
1. 浏览器兼容性
2. Web服务器配置
3. 文件权限设置
4. 网络连接状态

---

**部署成功标准**: 系统能够正常访问，所有功能正常工作，权限控制有效。
