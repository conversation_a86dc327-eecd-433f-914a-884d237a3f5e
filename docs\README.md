# AI调研助手 - 智能数据分析平台

## 概述

AI调研助手是一个功能完整的智能数据分析平台，支持双角色权限体系，为内部使用而设计。系统提供了完整的用户权限管理、AI模型配置、搜索引擎管理、Prompt模板库、任务管理等功能，并内置完整的Mock API系统用于开发和演示。

## 🚀 新功能特性

### 1. 增强的搜索功能
- **智能模型选择**：支持多种AI模型（GPT-4、Claude、Gemini等）
- **多引擎搜索**：集成Google、Bing、百度等多个搜索引擎
- **搜索过滤**：用户可快速搜索和筛选可用的模型和引擎
- **权限控制**：管理员配置，用户选择使用

### 2. 全新任务管理系统
- **统一任务视图**：查看所有任务的状态、进度和详细信息
- **高级筛选**：按状态、类型、时间范围筛选任务
- **批量操作**：支持批量启动、暂停、取消任务
- **实时进度**：任务执行进度实时更新

### 3. Prompt模板库
- **模板管理**：创建、编辑、删除自定义Prompt模板
- **官方模板**：内置专业的分析模板（市场分析、技术研究、竞争分析等）
- **智能分类**：按用途分类管理模板
- **一键使用**：在搜索页面直接选择和使用模板
- **收藏功能**：收藏常用模板

### 4. Mock API系统
- **完整API模拟**：涵盖所有主要功能的API接口
- **开发友好**：支持开发和演示模式
- **数据持久化**：使用localStorage模拟数据存储
- **一键切换**：可在Mock模式和生产模式间切换

### 5. 配置管理系统
- **模型配置**：管理员可添加、配置、启用/禁用AI模型
- **搜索引擎配置**：管理员可配置自定义搜索引擎
- **权限分离**：配置权限仅限管理员，用户只能选择使用
- **安全管理**：API密钥等敏感信息安全存储

## 权限系统架构

### 用户角色

#### 管理员 (admin)
- **用户名**: `admin`
- **密码**: `admin`
- **权限**: 拥有所有功能权限
- **功能访问**:
  - ✅ AI模型配置管理（添加、编辑、启用/禁用）
  - ✅ 搜索引擎配置管理（添加、编辑、启用/禁用）
  - ✅ 系统管理和用户管理
  - ✅ 全局统计数据查看
  - ✅ 任务管理（查看所有用户任务）
  - ✅ Prompt模板管理
  - ✅ 基础搜索和批量任务
  - ✅ 查看所有用户结果

#### 普通用户 (user)
- **用户名**: `user`
- **密码**: `user`
- **权限**: 基础功能权限
- **功能访问**:
  - ✅ 基础搜索功能（选择管理员配置的模型和引擎）
  - ✅ 批量任务处理
  - ✅ 任务管理（查看个人任务）
  - ✅ Prompt模板管理（创建和使用个人模板）
  - ✅ 查看个人结果
  - ❌ AI模型和搜索引擎配置
  - ❌ 系统管理功能
  - ❌ 查看其他用户数据

### 权限控制机制

1. **页面级权限控制**
   - 登录验证：未登录用户自动跳转到登录页面
   - 角色验证：普通用户无法访问管理员专属页面
   - 会话管理：支持24小时会话保持

2. **功能级权限控制**
   - 导航菜单：根据权限动态显示/隐藏菜单项
   - 按钮控制：敏感操作按钮仅对有权限用户显示
   - 数据展示：不同角色看到不同的统计数据

3. **数据级权限控制**
   - 管理员：可查看全系统数据和统计
   - 普通用户：仅可查看个人相关数据

## 页面功能说明

### 登录页面 (login.html)
- 用户认证入口
- 支持快速登录演示账户
- 会话状态管理
- 错误提示和成功反馈

### 仪表板 (dashboard.html)
- **管理员视图**: 全系统统计、活跃用户、系统负载监控
- **普通用户视图**: 个人搜索统计、API使用量
- 快速操作面板
- 最近任务列表

### 单次搜索 (search.html) - 🆕 增强功能
- 关键词输入和搜索配置
- **智能模型选择**: 支持搜索过滤，快速找到合适的AI模型
- **多引擎选择**: 支持搜索过滤，选择最适合的搜索引擎
- **Prompt模板**: 集成模板选择器，支持保存为模板
- 自定义Prompt编辑

### 批量任务 (batch.html) - 🆕 增强功能
- Excel文件上传和解析
- 列映射配置（关键词列、结果输出列、分类列等）
- **智能配置**: AI模型和搜索引擎选择
- 数据预览和验证
- 批量处理配置

### 任务管理 (task-management.html) - 🆕 全新页面
- **统一任务视图**: 查看所有任务状态和进度
- **高级筛选**: 按状态、类型、时间范围筛选
- **批量操作**: 启动、暂停、取消、导出
- **实时更新**: 任务进度实时刷新
- **权限控制**: 管理员查看所有，用户查看个人

### Prompt模板库 (prompt-templates.html) - 🆕 全新页面
- **模板管理**: 创建、编辑、删除Prompt模板
- **官方模板**: 内置专业分析模板
- **智能分类**: 市场分析、技术研究、竞争分析等
- **搜索功能**: 快速找到需要的模板
- **收藏系统**: 收藏常用模板

### 模型配置 (model-config.html) - 🆕 全新页面 (仅管理员)
- **AI模型管理**: 添加、编辑、启用/禁用AI模型
- **搜索引擎管理**: 配置自定义搜索引擎
- **安全配置**: API密钥和端点配置
- **状态控制**: 实时启用/禁用功能

### 结果管理 (results.html)
- 搜索结果展示和管理
- 高级筛选和排序
- 批量导出功能
- 质量评分显示

### 任务历史 (task-history.html)
- 任务历史记录查看
- 任务状态管理
- 任务重试和批量操作
- 详细筛选功能

### 任务详情 (task-detail.html)
- 任务执行详情展示
- 执行日志和时间线
- 使用的Prompt显示
- 分析结果预览

### 设置配置 (settings.html)
- **管理员视图**: 完整的配置管理界面
- **普通用户视图**: 简化的个人设置
- 个人偏好设置

### 系统管理 (admin.html) - 仅管理员
- 用户管理：添加、编辑、删除用户
- 使用统计和报表
- 系统日志查看

### 个人中心 (profile.html)
- 用户信息管理
- 使用统计展示
- 账户安全设置
- 最近活动记录

## 技术实现

### 核心系统文件

1. **auth.js** - 权限管理核心
   - 用户认证和会话管理
   - 权限检查和控制（修正权限逻辑）
   - AI模型和搜索引擎获取接口
   - 用户活动记录

2. **navigation.js** - 导航管理
   - 统一导航栏生成
   - 权限控制的菜单显示
   - 用户信息更新
   - 页面访问控制

3. **mock-api.js** - 🆕 Mock API系统
   - 完整的API模拟系统
   - 支持所有CRUD操作
   - 数据持久化到localStorage
   - 模拟网络延迟和错误处理

4. **app.js** - 应用核心功能
   - 通用UI组件
   - 事件处理
   - 工具函数

### 数据存储架构

- **localStorage**:
  - 用户会话信息和权限数据
  - AI模型和搜索引擎配置
  - Prompt模板数据
  - 任务和结果数据
- **sessionStorage**: 临时会话数据
- **Mock数据结构**:
  - 用户账户管理
  - 任务状态跟踪
  - 模板和配置管理

### 安全特性

1. **会话管理**
   - 24小时会话过期
   - 自动登录状态检查
   - 安全退出功能

2. **权限验证**
   - 页面加载时权限检查
   - 功能访问前权限验证
   - 敏感操作二次确认

3. **数据保护**
   - 密码信息不存储在会话中
   - 用户活动日志记录
   - 访问控制和审计

## 使用指南

### 快速开始

1. 打开 `index.html` 查看产品介绍
2. 点击"立即体验"进入登录页面
3. 使用演示账户登录：
   - 管理员：`admin` / `admin`
   - 普通用户：`user` / `user`

### 管理员操作流程

1. **系统配置**
   - 登录管理员账户
   - 访问"模型配置"管理AI模型和搜索引擎
   - 添加自定义模型和搜索引擎
   - 启用/禁用可用选项

2. **系统监控**
   - 查看"仪表板"监控系统状态
   - 在"任务管理"中查看所有用户任务
   - 访问"系统管理"查看使用统计

3. **内容管理**
   - 管理官方Prompt模板
   - 查看和管理用户创建的内容

### 普通用户操作流程

1. **搜索分析**
   - 登录普通用户账户
   - 在"单次搜索"中选择AI模型和搜索引擎
   - 使用或创建Prompt模板
   - 执行搜索分析

2. **批量处理**
   - 使用"批量任务"上传Excel文件
   - 配置列映射和处理参数
   - 监控批量任务进度

3. **结果管理**
   - 在"任务管理"中查看个人任务状态
   - 在"结果管理"中查看和导出结果
   - 管理个人Prompt模板库

## 开发说明

### 🔧 权限系统架构

**重要**: 系统采用严格的权限分离原则：
- **配置权限**: 仅管理员可以添加、编辑、启用/禁用AI模型和搜索引擎
- **使用权限**: 所有用户都可以从管理员配置的选项中选择使用

### 扩展权限系统

1. 在 `auth.js` 中添加新的权限常量
2. 在 `ROLE_PERMISSIONS` 中配置角色权限映射
3. 在页面中使用 `data-permission` 属性控制元素显示
4. 使用 `AuthManager.hasPermission()` 检查权限

### 添加新功能模块

1. **创建新页面**
   - 复制现有页面模板
   - 更新导航菜单 (`navigation.js`)
   - 添加权限检查

2. **集成Mock API**
   - 在 `mock-api.js` 中添加新的API路由
   - 实现CRUD操作
   - 添加数据验证和错误处理

3. **数据管理**
   - 设计localStorage数据结构
   - 实现数据持久化
   - 添加数据迁移逻辑

### Mock API系统使用

```javascript
// 调用Mock API示例
const response = await window.MockAPI.call('GET', '/tasks', { page: 1, pageSize: 10 });
if (response.success) {
    console.log(response.data);
} else {
    console.error(response.error);
}
```

### 自定义配置

- **AI模型管理**: 通过模型配置页面或直接修改localStorage
- **搜索引擎管理**: 通过配置界面添加自定义搜索引擎
- **权限控制**: 调整 `PERMISSIONS` 和 `ROLE_PERMISSIONS`
- **Mock数据**: 修改 `mock-api.js` 中的默认数据

## 注意事项

1. **系统性质**: 这是一个前端原型系统，使用Mock数据模拟后端功能
2. **生产部署**: 实际部署时需要替换为真实的后端API
3. **安全考虑**:
   - 密码和API密钥需要使用更安全的存储和传输方式
   - 建议在生产环境中使用更强的身份验证机制
   - 敏感配置应该通过环境变量管理
4. **权限逻辑**: 严格遵循配置与使用分离的原则
5. **数据持久化**: 当前使用localStorage，生产环境需要真实数据库

## 🔄 更新日志

### v2.0.0 - 2024-12-19 (当前版本)

#### 🆕 新增功能
- **任务管理系统**: 全新的统一任务管理界面
- **Prompt模板库**: 完整的模板管理和使用系统
- **Mock API系统**: 完整的API模拟框架
- **模型配置管理**: 专门的AI模型和搜索引擎配置页面
- **搜索过滤功能**: 在模型和引擎选择中添加搜索功能

#### 🔧 权限系统重构
- **修正权限逻辑**: 明确区分配置权限和使用权限
- **管理员专属**: AI模型和搜索引擎配置仅限管理员
- **用户友好**: 普通用户可以轻松选择和使用管理员配置的选项

#### 🎨 用户体验优化
- **响应式设计**: 所有新页面支持移动端适配
- **搜索过滤**: 快速找到需要的模型和引擎
- **批量操作**: 任务管理支持批量操作
- **实时更新**: 任务进度实时刷新

#### 🛠️ 技术改进
- **代码重构**: 优化权限检查逻辑
- **API标准化**: 统一的Mock API接口设计
- **数据管理**: 改进的localStorage数据结构
- **错误处理**: 完善的错误提示和处理机制

### v2.0.0 - 权限管理系统
- ✅ 实现双角色权限体系
- ✅ 添加登录认证系统
- ✅ 移除会员相关功能
- ✅ 增强Excel批量任务功能
- ✅ 添加任务管理页面
- ✅ 实现管理员系统管理功能
- ✅ 优化导航和用户界面
- ✅ 添加权限控制和安全机制
