<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型配置 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <!-- 页面标题和操作 -->
        <div class="d-flex justify-content-between items-center mb-4">
            <div>
                <h1 style="font-size: 1.5rem; font-weight: 600; margin: 0;">AI模型配置</h1>
                <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0;">管理系统可用的AI模型和配置</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline" onclick="refreshModels()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <button class="btn btn-primary" onclick="addCustomModel()">
                    <i class="fas fa-plus"></i> 添加自定义模型
                </button>
            </div>
        </div>

        <!-- 模型列表 -->
        <div class="card">
            <div class="card-header">
                <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                    <i class="fas fa-robot" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    AI模型管理
                </h3>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>模型名称</th>
                                <th>提供商</th>
                                <th>状态</th>
                                <th>类型</th>
                                <th>描述</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="modelsTableBody">
                            <!-- 模型数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 搜索引擎列表 -->
        <div class="card mt-4">
            <div class="card-header">
                <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                    <i class="fas fa-search" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                    搜索引擎管理
                </h3>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>引擎名称</th>
                                <th>状态</th>
                                <th>类型</th>
                                <th>描述</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="enginesTableBody">
                            <!-- 搜索引擎数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 添加自定义模型模态框 -->
        <div id="modelModal" class="modal" style="display: none;">
            <div class="modal-content" style="max-width: 600px;">
                <div class="modal-header">
                    <h3 id="modelModalTitle">添加自定义AI模型</h3>
                    <button class="modal-close" onclick="closeModelModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="modelForm">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div class="form-group">
                                <label class="form-label">模型ID *</label>
                                <input type="text" class="form-control" id="modelId" required placeholder="例如: custom-gpt-4">
                            </div>
                            <div class="form-group">
                                <label class="form-label">模型名称 *</label>
                                <input type="text" class="form-control" id="modelName" required placeholder="例如: Custom GPT-4">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div class="form-group">
                                <label class="form-label">提供商 *</label>
                                <input type="text" class="form-control" id="modelProvider" required placeholder="例如: OpenAI">
                            </div>
                            <div class="form-group">
                                <label class="form-label">API端点</label>
                                <input type="url" class="form-control" id="modelEndpoint" placeholder="https://api.example.com/v1/chat/completions">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">描述</label>
                            <textarea class="form-control" id="modelDescription" rows="2" placeholder="简要描述这个模型的特点和用途"></textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div class="form-group">
                                <label class="form-label">API密钥</label>
                                <input type="password" class="form-control" id="modelApiKey" placeholder="输入API密钥">
                            </div>
                            <div class="form-group">
                                <label class="form-label">默认状态</label>
                                <select class="form-control form-select" id="modelEnabled">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModelModal()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveModel()">保存模型</button>
                </div>
            </div>
        </div>

        <!-- 添加自定义搜索引擎模态框 -->
        <div id="engineModal" class="modal" style="display: none;">
            <div class="modal-content" style="max-width: 600px;">
                <div class="modal-header">
                    <h3 id="engineModalTitle">添加自定义搜索引擎</h3>
                    <button class="modal-close" onclick="closeEngineModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="engineForm">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div class="form-group">
                                <label class="form-label">引擎ID *</label>
                                <input type="text" class="form-control" id="engineId" required placeholder="例如: custom-search">
                            </div>
                            <div class="form-group">
                                <label class="form-label">引擎名称 *</label>
                                <input type="text" class="form-control" id="engineName" required placeholder="例如: 自定义搜索">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">搜索端点 *</label>
                            <input type="url" class="form-control" id="engineEndpoint" required placeholder="https://api.example.com/search?q={query}">
                            <div style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.5rem;">
                                使用 {query} 作为搜索关键词占位符
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">描述</label>
                            <textarea class="form-control" id="engineDescription" rows="2" placeholder="简要描述这个搜索引擎的特点"></textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div class="form-group">
                                <label class="form-label">API密钥</label>
                                <input type="password" class="form-control" id="engineApiKey" placeholder="输入API密钥（如需要）">
                            </div>
                            <div class="form-group">
                                <label class="form-label">默认状态</label>
                                <select class="form-control form-select" id="engineEnabled">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeEngineModal()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveEngine()">保存引擎</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/mock-api.js"></script>
    <script>
        let currentEditingModel = null;
        let currentEditingEngine = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查管理员权限
            if (!window.AuthManager.hasPermission('api_config')) {
                window.location.href = 'dashboard.html';
                return;
            }

            // 记录访问活动
            window.AuthManager.logActivity('访问AI模型配置页面');
            
            // 加载数据
            loadModels();
            loadEngines();
        });

        // 加载模型数据
        async function loadModels() {
            try {
                const response = await window.MockAPI.call('GET', '/models');
                if (response.success) {
                    renderModels(response.data);
                }
            } catch (error) {
                console.error('加载模型失败:', error);
                AIResearchApp.showNotification('加载模型失败', 'error');
            }
        }

        // 加载搜索引擎数据
        async function loadEngines() {
            try {
                const response = await window.MockAPI.call('GET', '/engines');
                if (response.success) {
                    renderEngines(response.data);
                }
            } catch (error) {
                console.error('加载搜索引擎失败:', error);
                AIResearchApp.showNotification('加载搜索引擎失败', 'error');
            }
        }

        // 渲染模型列表
        function renderModels(models) {
            const tbody = document.getElementById('modelsTableBody');
            tbody.innerHTML = '';

            models.forEach(model => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: 500;">${model.name}</div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">ID: ${model.id}</div>
                    </td>
                    <td>${model.provider}</td>
                    <td>
                        <span class="badge badge-${model.enabled ? 'success' : 'secondary'}">
                            ${model.enabled ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td>
                        <span class="badge badge-${model.id.startsWith('custom') ? 'warning' : 'primary'}">
                            ${model.id.startsWith('custom') ? '自定义' : '官方'}
                        </span>
                    </td>
                    <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                        ${model.description || '-'}
                    </td>
                    <td>
                        <div class="d-flex gap-1">
                            <button class="btn btn-outline btn-sm" onclick="toggleModelStatus('${model.id}', ${!model.enabled})" 
                                    title="${model.enabled ? '禁用' : '启用'}">
                                <i class="fas fa-${model.enabled ? 'pause' : 'play'}"></i>
                            </button>
                            ${model.id.startsWith('custom') ? `
                                <button class="btn btn-primary btn-sm" onclick="editModel('${model.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteModel('${model.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染搜索引擎列表
        function renderEngines(engines) {
            const tbody = document.getElementById('enginesTableBody');
            tbody.innerHTML = '';

            engines.forEach(engine => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: 500;">${engine.name}</div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">ID: ${engine.id}</div>
                    </td>
                    <td>
                        <span class="badge badge-${engine.enabled ? 'success' : 'secondary'}">
                            ${engine.enabled ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td>
                        <span class="badge badge-${engine.id.startsWith('custom') ? 'warning' : 'primary'}">
                            ${engine.id.startsWith('custom') ? '自定义' : '官方'}
                        </span>
                    </td>
                    <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                        ${engine.description || '-'}
                    </td>
                    <td>
                        <div class="d-flex gap-1">
                            <button class="btn btn-outline btn-sm" onclick="toggleEngineStatus('${engine.id}', ${!engine.enabled})"
                                    title="${engine.enabled ? '禁用' : '启用'}">
                                <i class="fas fa-${engine.enabled ? 'pause' : 'play'}"></i>
                            </button>
                            ${engine.id.startsWith('custom') ? `
                                <button class="btn btn-primary btn-sm" onclick="editEngine('${engine.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteEngine('${engine.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 切换模型状态
        async function toggleModelStatus(modelId, enabled) {
            try {
                const response = await window.MockAPI.call('PUT', `/models/${modelId}`, { enabled });
                if (response.success) {
                    AIResearchApp.showNotification(`模型已${enabled ? '启用' : '禁用'}`, 'success');
                    loadModels();
                }
            } catch (error) {
                AIResearchApp.showNotification('操作失败', 'error');
            }
        }

        // 切换搜索引擎状态
        async function toggleEngineStatus(engineId, enabled) {
            try {
                const response = await window.MockAPI.call('PUT', `/engines/${engineId}`, { enabled });
                if (response.success) {
                    AIResearchApp.showNotification(`搜索引擎已${enabled ? '启用' : '禁用'}`, 'success');
                    loadEngines();
                }
            } catch (error) {
                AIResearchApp.showNotification('操作失败', 'error');
            }
        }

        // 添加自定义模型
        function addCustomModel() {
            currentEditingModel = null;
            document.getElementById('modelModalTitle').textContent = '添加自定义AI模型';
            document.getElementById('modelForm').reset();
            document.getElementById('modelModal').style.display = 'flex';
        }

        // 编辑模型
        async function editModel(modelId) {
            try {
                const response = await window.MockAPI.call('GET', `/models/${modelId}`);
                if (response.success) {
                    const model = response.data;
                    currentEditingModel = model;

                    document.getElementById('modelModalTitle').textContent = '编辑AI模型';
                    document.getElementById('modelId').value = model.id;
                    document.getElementById('modelName').value = model.name;
                    document.getElementById('modelProvider').value = model.provider;
                    document.getElementById('modelEndpoint').value = model.config?.endpoint || '';
                    document.getElementById('modelDescription').value = model.description || '';
                    document.getElementById('modelApiKey').value = model.config?.apiKey || '';
                    document.getElementById('modelEnabled').value = model.enabled.toString();

                    document.getElementById('modelModal').style.display = 'flex';
                }
            } catch (error) {
                AIResearchApp.showNotification('加载模型信息失败', 'error');
            }
        }

        // 删除模型
        async function deleteModel(modelId) {
            if (confirm('确定要删除这个模型吗？此操作不可恢复。')) {
                try {
                    const response = await window.MockAPI.call('DELETE', `/models/${modelId}`);
                    if (response.success) {
                        AIResearchApp.showNotification('模型删除成功', 'success');
                        loadModels();
                    }
                } catch (error) {
                    AIResearchApp.showNotification('删除失败', 'error');
                }
            }
        }

        // 保存模型
        async function saveModel() {
            const form = document.getElementById('modelForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const modelData = {
                id: document.getElementById('modelId').value,
                name: document.getElementById('modelName').value,
                provider: document.getElementById('modelProvider').value,
                description: document.getElementById('modelDescription').value,
                enabled: document.getElementById('modelEnabled').value === 'true',
                config: {
                    endpoint: document.getElementById('modelEndpoint').value,
                    apiKey: document.getElementById('modelApiKey').value
                }
            };

            try {
                let response;
                if (currentEditingModel) {
                    response = await window.MockAPI.call('PUT', `/models/${currentEditingModel.id}`, modelData);
                } else {
                    response = await window.MockAPI.call('POST', '/models', modelData);
                }

                if (response.success) {
                    AIResearchApp.showNotification(currentEditingModel ? '模型更新成功' : '模型添加成功', 'success');
                    closeModelModal();
                    loadModels();
                }
            } catch (error) {
                AIResearchApp.showNotification('保存失败: ' + error.message, 'error');
            }
        }

        // 关闭模型模态框
        function closeModelModal() {
            document.getElementById('modelModal').style.display = 'none';
            currentEditingModel = null;
        }

        // 添加自定义搜索引擎
        function addCustomEngine() {
            currentEditingEngine = null;
            document.getElementById('engineModalTitle').textContent = '添加自定义搜索引擎';
            document.getElementById('engineForm').reset();
            document.getElementById('engineModal').style.display = 'flex';
        }

        // 编辑搜索引擎
        async function editEngine(engineId) {
            try {
                const response = await window.MockAPI.call('GET', `/engines/${engineId}`);
                if (response.success) {
                    const engine = response.data;
                    currentEditingEngine = engine;

                    document.getElementById('engineModalTitle').textContent = '编辑搜索引擎';
                    document.getElementById('engineId').value = engine.id;
                    document.getElementById('engineName').value = engine.name;
                    document.getElementById('engineEndpoint').value = engine.config?.endpoint || '';
                    document.getElementById('engineDescription').value = engine.description || '';
                    document.getElementById('engineApiKey').value = engine.config?.apiKey || '';
                    document.getElementById('engineEnabled').value = engine.enabled.toString();

                    document.getElementById('engineModal').style.display = 'flex';
                }
            } catch (error) {
                AIResearchApp.showNotification('加载搜索引擎信息失败', 'error');
            }
        }

        // 删除搜索引擎
        async function deleteEngine(engineId) {
            if (confirm('确定要删除这个搜索引擎吗？此操作不可恢复。')) {
                try {
                    const response = await window.MockAPI.call('DELETE', `/engines/${engineId}`);
                    if (response.success) {
                        AIResearchApp.showNotification('搜索引擎删除成功', 'success');
                        loadEngines();
                    }
                } catch (error) {
                    AIResearchApp.showNotification('删除失败', 'error');
                }
            }
        }

        // 保存搜索引擎
        async function saveEngine() {
            const form = document.getElementById('engineForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const engineData = {
                id: document.getElementById('engineId').value,
                name: document.getElementById('engineName').value,
                description: document.getElementById('engineDescription').value,
                enabled: document.getElementById('engineEnabled').value === 'true',
                config: {
                    endpoint: document.getElementById('engineEndpoint').value,
                    apiKey: document.getElementById('engineApiKey').value
                }
            };

            try {
                let response;
                if (currentEditingEngine) {
                    response = await window.MockAPI.call('PUT', `/engines/${currentEditingEngine.id}`, engineData);
                } else {
                    response = await window.MockAPI.call('POST', '/engines', engineData);
                }

                if (response.success) {
                    AIResearchApp.showNotification(currentEditingEngine ? '搜索引擎更新成功' : '搜索引擎添加成功', 'success');
                    closeEngineModal();
                    loadEngines();
                }
            } catch (error) {
                AIResearchApp.showNotification('保存失败: ' + error.message, 'error');
            }
        }

        // 关闭搜索引擎模态框
        function closeEngineModal() {
            document.getElementById('engineModal').style.display = 'none';
            currentEditingEngine = null;
        }

        // 刷新模型
        function refreshModels() {
            AIResearchApp.showNotification('正在刷新...', 'info');
            loadModels();
            loadEngines();
        }
    </script>
</body>
</html>
