// AI调研助手 - 用户权限管理系统

// 用户角色定义
const USER_ROLES = {
    ADMIN: 'admin',
    USER: 'user'
};

// 权限定义
const PERMISSIONS = {
    // API配置权限（仅管理员）
    API_CONFIG: 'api_config',
    // 搜索引擎配置权限（仅管理员）
    SEARCH_ENGINE_CONFIG: 'search_engine_config',
    // 自定义数据源管理权限（仅管理员）
    CUSTOM_DATASOURCE: 'custom_datasource',
    // 系统管理权限（仅管理员）
    SYSTEM_ADMIN: 'system_admin',
    // 用户管理权限（仅管理员）
    USER_MANAGEMENT: 'user_management',
    // 全局统计查看权限（仅管理员）
    GLOBAL_STATS: 'global_stats',
    // 基础搜索权限（所有用户）
    BASIC_SEARCH: 'basic_search',
    // 批量任务权限（所有用户）
    BATCH_TASKS: 'batch_tasks',
    // 查看个人结果权限（所有用户）
    VIEW_OWN_RESULTS: 'view_own_results',
    // Prompt模板管理权限（所有用户）
    PROMPT_TEMPLATE_MANAGE: 'prompt_template_manage',
    // 任务管理权限（管理员查看所有，用户查看自己）
    TASK_MANAGEMENT: 'task_management'
};

// 角色权限映射
const ROLE_PERMISSIONS = {
    [USER_ROLES.ADMIN]: [
        // 管理员拥有所有权限
        PERMISSIONS.API_CONFIG,
        PERMISSIONS.SEARCH_ENGINE_CONFIG,
        PERMISSIONS.CUSTOM_DATASOURCE,
        PERMISSIONS.SYSTEM_ADMIN,
        PERMISSIONS.USER_MANAGEMENT,
        PERMISSIONS.GLOBAL_STATS,
        PERMISSIONS.BASIC_SEARCH,
        PERMISSIONS.BATCH_TASKS,
        PERMISSIONS.VIEW_OWN_RESULTS,
        PERMISSIONS.PROMPT_TEMPLATE_MANAGE,
        PERMISSIONS.TASK_MANAGEMENT
    ],
    [USER_ROLES.USER]: [
        // 普通用户只有基础功能权限
        PERMISSIONS.BASIC_SEARCH,
        PERMISSIONS.BATCH_TASKS,
        PERMISSIONS.VIEW_OWN_RESULTS,
        PERMISSIONS.PROMPT_TEMPLATE_MANAGE,
        PERMISSIONS.TASK_MANAGEMENT
    ]
};

// Mock用户数据
const MOCK_USERS = {
    admin: {
        username: 'admin',
        password: 'admin',
        role: USER_ROLES.ADMIN,
        name: '系统管理员',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        lastLogin: new Date().toISOString(),
        permissions: ROLE_PERMISSIONS[USER_ROLES.ADMIN]
    },
    user: {
        username: 'user',
        password: 'user',
        role: USER_ROLES.USER,
        name: '普通用户',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face',
        lastLogin: new Date().toISOString(),
        permissions: ROLE_PERMISSIONS[USER_ROLES.USER]
    }
};

// 权限管理类
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.sessionKey = 'airesearch_user_session';
        this.init();
    }

    // 初始化权限管理器
    init() {
        this.loadUserSession();
        this.setupPageProtection();
    }

    // 用户登录
    login(username, password) {
        const user = MOCK_USERS[username];
        
        if (!user || user.password !== password) {
            return {
                success: false,
                message: '用户名或密码错误'
            };
        }

        // 更新最后登录时间
        user.lastLogin = new Date().toISOString();
        
        // 保存用户会话
        this.currentUser = { ...user };
        delete this.currentUser.password; // 移除密码信息
        
        this.saveUserSession();
        
        return {
            success: true,
            message: '登录成功',
            user: this.currentUser
        };
    }

    // 用户登出
    logout() {
        this.currentUser = null;
        localStorage.removeItem(this.sessionKey);
        sessionStorage.removeItem(this.sessionKey);
        
        // 重定向到登录页面
        if (window.location.pathname !== '/docs/login.html' && 
            !window.location.pathname.endsWith('login.html')) {
            window.location.href = 'login.html';
        }
    }

    // 检查用户是否已登录
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }

    // 检查用户是否有特定权限
    hasPermission(permission) {
        if (!this.isLoggedIn()) {
            return false;
        }
        
        return this.currentUser.permissions.includes(permission);
    }

    // 检查用户角色
    hasRole(role) {
        if (!this.isLoggedIn()) {
            return false;
        }
        
        return this.currentUser.role === role;
    }

    // 是否为管理员
    isAdmin() {
        return this.hasRole(USER_ROLES.ADMIN);
    }

    // 保存用户会话
    saveUserSession() {
        const sessionData = {
            user: this.currentUser,
            timestamp: Date.now()
        };
        
        localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
        sessionStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
    }

    // 加载用户会话
    loadUserSession() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey) || 
                               sessionStorage.getItem(this.sessionKey);
            
            if (sessionData) {
                const parsed = JSON.parse(sessionData);
                
                // 检查会话是否过期（24小时）
                const sessionAge = Date.now() - parsed.timestamp;
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                
                if (sessionAge < maxAge) {
                    this.currentUser = parsed.user;
                    return true;
                }
            }
        } catch (error) {
            console.error('加载用户会话失败:', error);
        }
        
        return false;
    }

    // 页面保护设置
    setupPageProtection() {
        // 如果不是登录页面且用户未登录，重定向到登录页面
        const currentPath = window.location.pathname;
        const isLoginPage = currentPath.includes('login.html');
        
        if (!isLoginPage && !this.isLoggedIn()) {
            window.location.href = 'login.html';
            return;
        }

        // 如果是登录页面且用户已登录，重定向到仪表板
        if (isLoginPage && this.isLoggedIn()) {
            window.location.href = 'dashboard.html';
            return;
        }

        // 检查页面访问权限
        this.checkPageAccess();
    }

    // 检查页面访问权限
    checkPageAccess() {
        const currentPath = window.location.pathname;
        const fileName = currentPath.split('/').pop();
        
        // 定义需要特殊权限的页面
        const restrictedPages = {
            'admin.html': [PERMISSIONS.SYSTEM_ADMIN],
            'settings.html': [], // 设置页面内部会根据权限显示不同内容
        };

        // 检查当前页面是否需要特殊权限
        if (restrictedPages[fileName]) {
            const requiredPermissions = restrictedPages[fileName];
            
            for (const permission of requiredPermissions) {
                if (!this.hasPermission(permission)) {
                    this.showAccessDenied();
                    return;
                }
            }
        }
    }

    // 显示访问被拒绝页面
    showAccessDenied() {
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; background: #f3f4f6;">
                <div style="text-align: center; background: white; padding: 3rem; border-radius: 1rem; box-shadow: 0 10px 25px rgba(0,0,0,0.1);">
                    <i class="fas fa-lock" style="font-size: 4rem; color: #ef4444; margin-bottom: 1rem;"></i>
                    <h1 style="font-size: 2rem; margin-bottom: 1rem; color: #1f2937;">访问被拒绝</h1>
                    <p style="color: #6b7280; margin-bottom: 2rem;">您没有权限访问此页面</p>
                    <button onclick="window.location.href='dashboard.html'" style="background: #3b82f6; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 0.5rem; cursor: pointer;">
                        返回仪表板
                    </button>
                </div>
            </div>
        `;
    }

    // 根据权限控制元素显示/隐藏
    controlElementVisibility() {
        // 控制导航菜单项
        const navItems = document.querySelectorAll('[data-permission]');
        navItems.forEach(item => {
            const requiredPermission = item.getAttribute('data-permission');
            if (!this.hasPermission(requiredPermission)) {
                item.style.display = 'none';
            }
        });

        // 控制角色相关元素
        const adminElements = document.querySelectorAll('[data-role="admin"]');
        adminElements.forEach(element => {
            if (!this.isAdmin()) {
                element.style.display = 'none';
            }
        });

        const userElements = document.querySelectorAll('[data-role="user"]');
        userElements.forEach(element => {
            if (this.isAdmin()) {
                element.style.display = 'none';
            }
        });
    }

    // 更新用户界面信息
    updateUserInterface() {
        if (!this.isLoggedIn()) return;

        // 更新用户头像
        const avatarElements = document.querySelectorAll('.user-avatar');
        avatarElements.forEach(element => {
            element.src = this.currentUser.avatar;
            element.alt = this.currentUser.name;
        });

        // 更新用户名称
        const nameElements = document.querySelectorAll('.user-name');
        nameElements.forEach(element => {
            element.textContent = this.currentUser.name;
        });

        // 更新角色标识
        const roleElements = document.querySelectorAll('.user-role');
        roleElements.forEach(element => {
            element.textContent = this.currentUser.role === USER_ROLES.ADMIN ? '管理员' : '普通用户';
            element.className = `user-role badge ${this.currentUser.role === USER_ROLES.ADMIN ? 'badge-warning' : 'badge-primary'}`;
        });

        // 控制元素可见性
        this.controlElementVisibility();
    }

    // 获取可用的AI模型列表（根据权限）
    getAvailableAIModels() {
        // 这里可以根据管理员配置动态返回
        const allModels = [
            { id: 'gpt4', name: 'GPT-4', description: '最强分析能力', enabled: true },
            { id: 'gpt35', name: 'GPT-3.5', description: '快速响应', enabled: true },
            { id: 'claude', name: 'Claude', description: '深度理解', enabled: false }
        ];

        // 普通用户只能看到启用的模型
        if (!this.isAdmin()) {
            return allModels.filter(model => model.enabled);
        }

        return allModels;
    }

    // 获取可用的搜索引擎列表（根据权限）
    getAvailableSearchEngines() {
        const allEngines = [
            { id: 'google', name: 'Google', enabled: true },
            { id: 'baidu', name: '百度', enabled: true },
            { id: 'bing', name: '必应', enabled: false },
            { id: 'scholar', name: '学术搜索', enabled: true }
        ];

        // 普通用户只能看到启用的搜索引擎
        if (!this.isAdmin()) {
            return allEngines.filter(engine => engine.enabled);
        }

        return allEngines;
    }

    // 记录用户活动
    logActivity(action, details = {}) {
        const activity = {
            timestamp: new Date().toISOString(),
            user: this.currentUser?.username,
            action: action,
            details: details
        };

        // 这里可以发送到后端或存储在本地
        console.log('用户活动:', activity);
        
        // 存储到本地存储（用于演示）
        const activities = JSON.parse(localStorage.getItem('user_activities') || '[]');
        activities.unshift(activity);
        
        // 只保留最近100条记录
        if (activities.length > 100) {
            activities.splice(100);
        }
        
        localStorage.setItem('user_activities', JSON.stringify(activities));
    }

    // 获取用户活动记录
    getUserActivities(limit = 10) {
        const activities = JSON.parse(localStorage.getItem('user_activities') || '[]');

        // 如果不是管理员，只返回当前用户的活动
        if (!this.isAdmin()) {
            return activities
                .filter(activity => activity.user === this.currentUser?.username)
                .slice(0, limit);
        }

        return activities.slice(0, limit);
    }

    // 获取可用的AI模型列表（所有用户都可以调用，但看到的内容不同）
    getAvailableAIModels() {
        const defaultModels = [
            { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI', enabled: true, description: '最强分析能力，适合复杂任务' },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI', enabled: true, description: '快速响应，适合日常分析' },
            { id: 'claude-3-opus', name: 'Claude 3 Opus', provider: 'Anthropic', enabled: true, description: '深度理解，适合文本分析' },
            { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'Anthropic', enabled: true, description: '平衡性能，适合多种任务' },
            { id: 'gemini-pro', name: 'Gemini Pro', provider: 'Google', enabled: false, description: '多模态分析能力' },
            { id: 'llama-2-70b', name: 'Llama 2 70B', provider: 'Meta', enabled: false, description: '开源大模型' }
        ];

        // 从localStorage获取管理员配置的自定义模型
        const customModels = JSON.parse(localStorage.getItem('custom_ai_models') || '[]');

        // 合并默认模型和自定义模型
        const allModels = [...defaultModels, ...customModels];

        // 所有用户都可以获取模型列表，但普通用户只能看到启用的模型
        if (this.isAdmin()) {
            // 管理员可以看到所有模型（用于配置管理）
            return allModels;
        } else {
            // 普通用户只能看到启用的模型（用于选择使用）
            return allModels.filter(model => model.enabled);
        }
    }

    // 获取可用的搜索引擎列表（所有用户都可以调用，但看到的内容不同）
    getAvailableSearchEngines() {
        const defaultEngines = [
            { id: 'google', name: 'Google', enabled: true, description: '全球最大搜索引擎，覆盖面广' },
            { id: 'bing', name: 'Bing', enabled: true, description: '微软搜索引擎，企业信息丰富' },
            { id: 'baidu', name: '百度', enabled: true, description: '中文搜索引擎，本土化内容' },
            { id: 'duckduckgo', name: 'DuckDuckGo', enabled: false, description: '注重隐私的搜索引擎' },
            { id: 'yandex', name: 'Yandex', enabled: false, description: '俄语搜索引擎' },
            { id: 'scholar', name: 'Google Scholar', enabled: true, description: '学术文献搜索' }
        ];

        // 从localStorage获取管理员配置的自定义搜索引擎
        const customEngines = JSON.parse(localStorage.getItem('custom_search_engines') || '[]');

        // 合并默认引擎和自定义引擎
        const allEngines = [...defaultEngines, ...customEngines];

        // 所有用户都可以获取搜索引擎列表，但普通用户只能看到启用的引擎
        if (this.isAdmin()) {
            // 管理员可以看到所有搜索引擎（用于配置管理）
            return allEngines;
        } else {
            // 普通用户只能看到启用的搜索引擎（用于选择使用）
            return allEngines.filter(engine => engine.enabled);
        }
    }

    // 保存AI模型配置
    saveAIModelConfig(modelId, config) {
        if (!this.hasPermission(PERMISSIONS.API_CONFIG)) {
            throw new Error('没有权限配置AI模型');
        }

        const configs = JSON.parse(localStorage.getItem('ai_model_configs') || '{}');
        configs[modelId] = {
            ...config,
            updatedAt: new Date().toISOString(),
            updatedBy: this.currentUser?.username
        };
        localStorage.setItem('ai_model_configs', JSON.stringify(configs));

        this.logActivity(`配置AI模型: ${modelId}`);
    }

    // 保存搜索引擎配置
    saveSearchEngineConfig(engineId, config) {
        if (!this.hasPermission(PERMISSIONS.SEARCH_ENGINE_CONFIG)) {
            throw new Error('没有权限配置搜索引擎');
        }

        const configs = JSON.parse(localStorage.getItem('search_engine_configs') || '{}');
        configs[engineId] = {
            ...config,
            updatedAt: new Date().toISOString(),
            updatedBy: this.currentUser?.username
        };
        localStorage.setItem('search_engine_configs', JSON.stringify(configs));

        this.logActivity(`配置搜索引擎: ${engineId}`);
    }

    // 添加自定义AI模型
    addCustomAIModel(model) {
        if (!this.hasPermission(PERMISSIONS.API_CONFIG)) {
            throw new Error('没有权限添加自定义AI模型');
        }

        const customModels = JSON.parse(localStorage.getItem('custom_ai_models') || '[]');
        const newModel = {
            ...model,
            id: model.id || `custom_${Date.now()}`,
            createdAt: new Date().toISOString(),
            createdBy: this.currentUser?.username
        };

        customModels.push(newModel);
        localStorage.setItem('custom_ai_models', JSON.stringify(customModels));

        this.logActivity(`添加自定义AI模型: ${newModel.name}`);
        return newModel;
    }

    // 添加自定义搜索引擎
    addCustomSearchEngine(engine) {
        if (!this.hasPermission(PERMISSIONS.SEARCH_ENGINE_CONFIG)) {
            throw new Error('没有权限添加自定义搜索引擎');
        }

        const customEngines = JSON.parse(localStorage.getItem('custom_search_engines') || '[]');
        const newEngine = {
            ...engine,
            id: engine.id || `custom_${Date.now()}`,
            createdAt: new Date().toISOString(),
            createdBy: this.currentUser?.username
        };

        customEngines.push(newEngine);
        localStorage.setItem('custom_search_engines', JSON.stringify(customEngines));

        this.logActivity(`添加自定义搜索引擎: ${newEngine.name}`);
        return newEngine;
    }
}

// 创建全局权限管理器实例
window.AuthManager = new AuthManager();

// 页面加载完成后初始化用户界面
document.addEventListener('DOMContentLoaded', () => {
    if (window.AuthManager.isLoggedIn()) {
        window.AuthManager.updateUserInterface();
    }
});

// 导出权限相关常量和类
window.USER_ROLES = USER_ROLES;
window.PERMISSIONS = PERMISSIONS;
window.ROLE_PERMISSIONS = ROLE_PERMISSIONS;
