# AI调研助手 - 功能测试指南

## 测试概述

本文档提供了AI调研助手系统的完整功能测试指南，包括权限验证、新功能测试和用户体验验证。

## 🔐 权限系统测试

### 管理员权限测试

**登录信息**: `admin` / `admin`

#### 1. 基础权限验证
- [ ] 能够访问所有页面
- [ ] 导航菜单显示所有选项（包括"模型配置"和"系统管理"）
- [ ] 仪表板显示全局统计数据

#### 2. 模型配置权限
- [ ] 能够访问"模型配置"页面
- [ ] 能够查看所有AI模型（包括禁用的）
- [ ] 能够启用/禁用AI模型
- [ ] 能够添加自定义AI模型
- [ ] 能够编辑和删除自定义模型
- [ ] 能够配置搜索引擎

#### 3. 任务管理权限
- [ ] 在任务管理页面能看到所有用户的任务
- [ ] 能够管理其他用户的任务
- [ ] 统计数据包含全系统数据

### 普通用户权限测试

**登录信息**: `user` / `user`

#### 1. 基础权限验证
- [ ] 无法访问"模型配置"页面（导航菜单中不显示）
- [ ] 无法访问"系统管理"页面
- [ ] 仪表板只显示个人统计数据

#### 2. 功能使用权限
- [ ] 能够在搜索页面选择AI模型（只显示启用的模型）
- [ ] 能够选择搜索引擎（只显示启用的引擎）
- [ ] 搜索页面不显示"管理模型"和"管理搜索引擎"按钮
- [ ] 能够创建和管理个人Prompt模板

#### 3. 数据访问权限
- [ ] 任务管理页面只显示个人任务
- [ ] 结果管理只显示个人结果
- [ ] 无法查看其他用户的数据

## 🆕 新功能测试

### 1. 搜索功能优化测试

#### 单次搜索页面
- [ ] AI模型选择区域显示搜索框
- [ ] 输入模型名称能够正确过滤
- [ ] 搜索引擎选择区域显示搜索框
- [ ] 输入引擎名称能够正确过滤
- [ ] 模型和引擎显示描述信息
- [ ] 选择模型后样式正确更新

#### 批量任务页面
- [ ] AI模型下拉框包含所有可用模型
- [ ] 搜索引擎选择区域正确显示
- [ ] 能够选择多个搜索引擎
- [ ] 配置保存正确

### 2. 任务管理系统测试

#### 任务列表功能
- [ ] 显示任务统计卡片（总数、运行中、已完成、失败）
- [ ] 任务列表正确显示所有字段
- [ ] 筛选功能正常工作（状态、类型、时间范围）
- [ ] 搜索功能能够按任务名称和关键词搜索
- [ ] 排序功能正常工作

#### 批量操作功能
- [ ] 全选功能正常工作
- [ ] 选择任务后显示批量操作工具栏
- [ ] 批量启动功能正常
- [ ] 批量暂停功能正常
- [ ] 批量取消功能正常
- [ ] 批量导出功能正常

#### 单个任务操作
- [ ] 查看任务详情功能
- [ ] 启动任务功能
- [ ] 暂停任务功能
- [ ] 重试任务功能
- [ ] 删除任务功能
- [ ] 下载结果功能

### 3. Prompt模板库测试

#### 模板浏览功能
- [ ] 显示官方模板和个人模板
- [ ] 分类筛选功能正常
- [ ] 来源筛选功能正常
- [ ] 搜索功能能够按名称、描述、标签搜索
- [ ] 排序功能正常工作
- [ ] 分页功能正常

#### 模板管理功能
- [ ] 能够创建新模板
- [ ] 能够编辑个人模板
- [ ] 能够删除个人模板
- [ ] 不能编辑官方模板
- [ ] 收藏功能正常工作

#### 模板使用功能
- [ ] 预览模板功能正常
- [ ] 从模板库使用模板跳转到搜索页面
- [ ] 搜索页面模板选择器正常工作
- [ ] 选择模板后自动填充内容
- [ ] 保存为模板功能正常

### 4. Mock API系统测试

#### API调用测试
- [ ] 所有API调用都有适当的延迟模拟
- [ ] API返回正确的数据格式
- [ ] 错误处理正常工作
- [ ] 权限验证正确执行

#### 数据持久化测试
- [ ] 创建的数据能够持久保存
- [ ] 页面刷新后数据仍然存在
- [ ] 不同用户的数据正确隔离
- [ ] 数据更新正确同步

### 5. 配置管理系统测试

#### 模型配置测试（仅管理员）
- [ ] 能够查看所有模型状态
- [ ] 能够启用/禁用模型
- [ ] 能够添加自定义模型
- [ ] 自定义模型配置保存正确
- [ ] 模型配置更改立即生效

#### 搜索引擎配置测试（仅管理员）
- [ ] 能够查看所有搜索引擎状态
- [ ] 能够启用/禁用搜索引擎
- [ ] 能够添加自定义搜索引擎
- [ ] 搜索引擎配置保存正确
- [ ] 配置更改立即生效

## 📱 响应式设计测试

### 桌面端测试 (1920x1080)
- [ ] 所有页面布局正常
- [ ] 导航菜单完整显示
- [ ] 表格和卡片布局合理
- [ ] 模态框居中显示

### 平板端测试 (768x1024)
- [ ] 导航菜单适配正常
- [ ] 网格布局自动调整
- [ ] 表格支持横向滚动
- [ ] 按钮大小适中

### 移动端测试 (375x667)
- [ ] 侧边栏可以正常收起/展开
- [ ] 表格在小屏幕上可用
- [ ] 模态框适配屏幕大小
- [ ] 触摸操作友好

## 🔍 用户体验测试

### 导航体验
- [ ] 页面间跳转流畅
- [ ] 面包屑导航正确
- [ ] 返回按钮功能正常
- [ ] 页面标题正确显示

### 交互体验
- [ ] 按钮点击反馈及时
- [ ] 加载状态显示清晰
- [ ] 错误提示友好
- [ ] 成功操作有确认反馈

### 性能体验
- [ ] 页面加载速度合理
- [ ] 大量数据渲染流畅
- [ ] 搜索过滤响应及时
- [ ] 无明显的界面卡顿

## 🐛 已知问题和限制

### 当前限制
1. **数据存储**: 使用localStorage，浏览器清除数据会丢失
2. **并发处理**: Mock API不支持真实的并发任务处理
3. **文件上传**: 批量任务的文件上传仅为演示功能
4. **实时通信**: 任务进度更新为模拟，非真实WebSocket连接

### 测试注意事项
1. **数据重置**: 清除浏览器localStorage可以重置所有数据
2. **权限测试**: 需要分别使用管理员和普通用户账户测试
3. **功能演示**: 某些功能为演示性质，不会产生真实的搜索结果
4. **浏览器兼容**: 建议使用现代浏览器进行测试

## 📋 测试检查清单

### 基础功能测试
- [ ] 用户登录/登出
- [ ] 权限控制
- [ ] 页面导航
- [ ] 数据显示

### 新功能测试
- [ ] 搜索功能优化
- [ ] 任务管理系统
- [ ] Prompt模板库
- [ ] Mock API系统
- [ ] 配置管理系统

### 用户体验测试
- [ ] 响应式设计
- [ ] 交互体验
- [ ] 性能表现
- [ ] 错误处理

### 权限验证测试
- [ ] 管理员权限
- [ ] 普通用户权限
- [ ] 页面访问控制
- [ ] 功能权限控制

---

**测试完成标准**: 所有测试项目通过，无阻塞性问题，用户体验良好。
